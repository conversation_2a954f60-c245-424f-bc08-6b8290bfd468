import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../ai/flexible_ai_service.dart';
import '../ai/flexible_action_executor.dart';
import '../voice/speech_service.dart';
import '../providers/app_provider.dart';

class FlexibleAIAssistant extends StatefulWidget {
  const FlexibleAIAssistant({super.key});

  @override
  State<FlexibleAIAssistant> createState() => _FlexibleAIAssistantState();
}

class _FlexibleAIAssistantState extends State<FlexibleAIAssistant> {
  final TextEditingController _textController = TextEditingController();
  bool _isListening = false;
  bool _isProcessing = false;
  bool _allowDataModification = true;

  // سجل المحادثة
  final List<Map<String, dynamic>> _chatHistory = [];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // Corregir el error de RenderFlex
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'المساعد الذكي المرن',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimary,
                ),
              ),
              const Spacer(),
              // Toggle data modification
              Switch(
                value: _allowDataModification,
                onChanged: (value) {
                  setState(() {
                    _allowDataModification = value;
                  });
                },
                activeColor: AppTheme.primary,
                activeTrackColor: AppTheme.primary.withValues(alpha: 0.3),
              ),
              const SizedBox(width: 8),
              Text(
                'تعديل البيانات',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Chat history
          if (_chatHistory.isNotEmpty)
            Flexible(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _chatHistory.length,
                reverse: true,
                itemBuilder: (context, index) {
                  final entry = _chatHistory[_chatHistory.length - 1 - index];
                  final isUser = entry['isUser'] as bool;
                  final message = entry['message'] as String;

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Row(
                      mainAxisAlignment: isUser
                          ? MainAxisAlignment.end
                          : MainAxisAlignment.start,
                      children: [
                        if (!isUser) ...[
                          CircleAvatar(
                            radius: 16,
                            backgroundColor: AppTheme.primary,
                            child: const Icon(
                              Icons.auto_awesome,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                        Flexible(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: isUser
                                  ? AppTheme.primary.withValues(alpha: 0.2)
                                  : AppTheme.surfaceBg.withValues(alpha: 0.5),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isUser
                                    ? AppTheme.primary.withValues(alpha: 0.3)
                                    : Colors.white.withValues(alpha: 0.1),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              message,
                              style: GoogleFonts.cairo(
                                color: AppTheme.textPrimary,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        if (isUser) ...[
                          const SizedBox(width: 8),
                          CircleAvatar(
                            radius: 16,
                            backgroundColor: AppTheme.accentPink,
                            child: const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                },
              ),
            ),

          const SizedBox(height: 16),

          // Text input
          TextField(
            controller: _textController,
            style: GoogleFonts.cairo(color: AppTheme.textPrimary),
            decoration: InputDecoration(
              hintText: 'اكتب أي سؤال أو طلب...',
              hintStyle: GoogleFonts.cairo(color: AppTheme.textMuted),
              filled: true,
              fillColor: AppTheme.surfaceBg.withValues(alpha: 0.5),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Voice button
                  IconButton(
                    onPressed: _isProcessing ? null : _toggleListening,
                    icon: Icon(
                      _isListening ? Icons.mic_rounded : Icons.mic_none_rounded,
                      color: _isListening ? AppTheme.danger : AppTheme.primary,
                    ),
                  ),
                  // Send button
                  IconButton(
                    onPressed: _isProcessing ? null : _processTextCommand,
                    icon: Icon(Icons.send_rounded, color: AppTheme.primary),
                  ),
                ],
              ),
            ),
            onSubmitted: (_) => _processTextCommand(),
            maxLines: 3,
            minLines: 1,
          ),

          const SizedBox(height: 16),

          // Processing indicator
          if (_isProcessing)
            Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation(AppTheme.primary),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'جاري المعالجة...',
                  style: GoogleFonts.cairo(
                    color: AppTheme.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Future<void> _toggleListening() async {
    if (_isListening) {
      SpeechService.stopListening();
      setState(() => _isListening = false);
    } else {
      setState(() => _isListening = true);
      final text = await SpeechService.startListening();
      setState(() => _isListening = false);

      if (text != null && text.isNotEmpty) {
        _textController.text = text;
        await _processCommand(text);
      }
    }
  }

  Future<void> _processTextCommand() async {
    final text = _textController.text.trim();
    if (text.isEmpty) return;

    // إضافة رسالة المستخدم إلى سجل المحادثة
    setState(() {
      _chatHistory.add({
        'isUser': true,
        'message': text,
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    await _processCommand(text);
    _textController.clear();
  }

  Future<void> _processCommand(String command) async {
    // Capturar el contexto antes de la operación asíncrona
    final provider = Provider.of<AppProvider>(context, listen: false);

    setState(() {
      _isProcessing = true;
    });

    try {
      // معالجة الطلب باستخدام الذكاء الاصطناعي المتقدم والمرن
      final aiAction = await FlexibleAIService.processRequest(
        command,
        provider,
        allowDataModification: _allowDataModification,
        allowSystemControl: true, // تفعيل التحكم في النظام
        allowAdvancedActions: true, // تفعيل الإجراءات المتقدمة
      );

      if (aiAction != null && mounted) {
        // تنفيذ الإجراء
        final result = await FlexibleActionExecutor.executeAction(
          aiAction,
          provider,
        );

        if (mounted) {
          setState(() {
            // إضافة رد المساعد إلى سجل المحادثة
            _chatHistory.add({
              'isUser': false,
              'message': result,
              'timestamp': DateTime.now().toIso8601String(),
              'action': aiAction['action'],
            });
          });
        }

        // نطق الرد
        await SpeechService.speak(result);
      } else if (mounted) {
        final errorMessage = '''
🤖 عذراً، لم أتمكن من فهم طلبك بشكل كامل.

💡 يمكنك تجربة:
• إعادة صياغة الطلب بوضوح أكثر
• استخدام كلمات مفتاحية مثل "أضف"، "احذف"، "عرض"، "حلل"
• طلب المساعدة بكتابة "مساعدة"

🎯 أنا قادر على فعل أي شيء تقريباً في إدارة التعليم!

🔥 أمثلة على ما يمكنني فعله:
• "أضف 10 طلاب جدد"
• "حلل أداء جميع المجموعات"
• "أنشئ تقرير شامل"
• "حسن الجداول الزمنية"
• "اقترح تحسينات للنظام"
''';

        setState(() {
          // إضافة رسالة الخطأ إلى سجل المحادثة
          _chatHistory.add({
            'isUser': false,
            'message': errorMessage,
            'timestamp': DateTime.now().toIso8601String(),
          });
        });
      }
    } catch (e) {
      if (mounted) {
        final errorMessage =
            '''
❌ حدث خطأ أثناء معالجة طلبك: $e

🔧 حلول مقترحة:
• تأكد من اتصالك بالإنترنت
• جرب إعادة صياغة الطلب
• تأكد من صحة البيانات المدخلة
• جرب استخدام أوامر أبسط

💪 لا تقلق، أنا هنا لمساعدتك!
🚀 جرب مرة أخرى بطريقة مختلفة.
''';

        setState(() {
          // إضافة رسالة الخطأ المحسنة إلى سجل المحادثة
          _chatHistory.add({
            'isUser': false,
            'message': errorMessage,
            'timestamp': DateTime.now().toIso8601String(),
          });
        });
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    SpeechService.stopListening();
    SpeechService.stopSpeaking();
    super.dispose();
  }
}
