import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'services/data_service.dart';
import 'services/storage_service.dart';
import 'providers/app_provider.dart';
import 'screens/startup_screen.dart';
import 'theme/app_theme.dart';
import 'utils/device_utils.dart';
import 'utils/memory_utils.dart';
import 'utils/performance_utils.dart';
import 'utils/image_optimizer.dart';
import 'utils/render_optimizer.dart';
import 'utils/stability_utils.dart';
// No se necesita importar flexible_ai_service.dart aquí

void main() async {
  try {
    // تهيئة Flutter أولاً
    WidgetsFlutterBinding.ensureInitialized();

    // تهيئة أدوات الاستقرار
    StabilityUtils.init();

    // تهيئة نظام الذكاء الاصطناعي المحسن
    await _initializeAI();

    // تشغيل التطبيق
    runApp(const EduTrackApp());

    // تنفيذ العمليات الثقيلة بعد عرض التطبيق
    _initializeAppAsync();
  } catch (e) {
    // تشغيل التطبيق حتى لو فشلت التهيئة
    runApp(const EduTrackApp());
  }
}

Future<void> _initializeAI() async {
  try {
    // لا توجد تهيئة خاصة مطلوبة للذكاء الاصطناعي المرن
    debugPrint('تم تهيئة نظام الذكاء الاصطناعي المرن');
  } catch (e) {
    debugPrint('خطأ في تهيئة نظام الذكاء الاصطناعي: $e');
  }
}

Future<void> _initializeAppAsync() async {
  try {
    // تحسينات الأداء الأساسية أولاً
    PerformanceUtils.optimizeAnimations();
    RenderOptimizer.applyGlobalOptimizations();

    // تعيين نمط واجهة النظام
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
      ),
    );

    // تعيين اتجاهات الشاشة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // تهيئة إدارة الذاكرة
    MemoryUtils.init();

    // تحسين كاش الصور
    final isLowEnd = DeviceUtils.isLowEndDevice();
    final imageCacheSize = isLowEnd ? 20 : 50;
    final imageCacheSizeBytes = isLowEnd ? 10 * 1024 * 1024 : 30 * 1024 * 1024;

    PaintingBinding.instance.imageCache.maximumSize = imageCacheSize;
    PaintingBinding.instance.imageCache.maximumSizeBytes = imageCacheSizeBytes;

    // تهيئة محسن الصور
    ImageOptimizer();

    // تنفيذ العمليات الثقيلة
    Future.microtask(() async {
      try {
        await Future.wait([
          initializeDateFormatting(
            'ar',
            null,
          ).timeout(const Duration(seconds: 5)),
          DataService.init().timeout(const Duration(seconds: 10)),
          StorageService.init().timeout(const Duration(seconds: 10)),
        ]);
      } catch (e) {
        // تجاهل الأخطاء
      }
    });
  } catch (e) {
    // تجاهل أخطاء التهيئة
  }
}

class EduTrackApp extends StatefulWidget {
  const EduTrackApp({super.key});

  @override
  State<EduTrackApp> createState() => _EduTrackAppState();
}

class _EduTrackAppState extends State<EduTrackApp> {
  @override
  void dispose() {
    MemoryUtils.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AppProvider(),
      child: MaterialApp(
        title: 'EduTrack',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.darkTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.dark,
        home: const StartupScreen(),
        // Performance optimizations
        builder: (context, child) {
          // Apply text scaling for better readability while maintaining performance
          return MediaQuery(
            data: MediaQuery.of(
              context,
            ).copyWith(textScaler: const TextScaler.linear(1.0)),
            child: child!,
          );
        },
        // Reduce unnecessary rebuilds
        restorationScopeId: 'edutrack_app',
      ),
    );
  }
}

class LoadingScreen extends StatelessWidget {
  const LoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBg,
      body: Center(
        child: CircularProgressIndicator(color: AppTheme.primaryBlue),
      ),
    );
  }
}
