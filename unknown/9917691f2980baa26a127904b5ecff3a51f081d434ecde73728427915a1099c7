import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'device_utils.dart';

/// Utility class for advanced performance optimizations
class AdvancedPerformance {
  /// Optimize a widget tree for low-end devices
  static Widget optimizeWidgetTree(Widget child) {
    if (!DeviceUtils.isLowEndDevice()) {
      return child;
    }
    
    // Apply optimizations for low-end devices
    return RepaintBoundary(child: child);
  }
  
  /// Schedule a task to run when the app is idle
  static void scheduleIdleTask(VoidCallback task) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // Check if we're in an idle period
      if (SchedulerBinding.instance.schedulerPhase == SchedulerPhase.idle) {
        task();
      } else {
        // Reschedule for next frame
        scheduleIdleTask(task);
      }
    });
  }
}