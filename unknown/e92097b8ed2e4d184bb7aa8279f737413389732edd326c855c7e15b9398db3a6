# إعداد EduTrack للأندرويد

## المتطلبات
- Flutter SDK
- Android Studio أو Android SDK
- جهاز أندرويد أو محاكي

## خطوات التشغيل

### 1. التحقق من الإعداد
```bash
flutter doctor
```

### 2. تشغيل التطبيق
```bash
# تشغيل مباشر
flutter run

# أو استخدام الملف المساعد
run_android.bat
```

### 3. بناء APK للتوزيع
```bash
# بناء APK
flutter build apk --release

# أو استخدام الملف المساعد
build_android.bat
```

## الإعدادات المطبقة

### ✅ صلاحيات Android
- `INTERNET` - للاتصال بالإنترنت
- `ACCESS_NETWORK_STATE` - لمراقبة حالة الشبكة

### ✅ إعدادات البناء
- `minSdk: 21` - دعم Android 5.0+
- `targetSdk: 34` - أحدث إصدار
- `multiDexEnabled: true` - دعم التطبيقات الكبيرة

### ✅ تحسينات الأداء
- ProGuard للتشفير وتقليل الحجم
- R8 للتحسين المتقدم
- Gradle caching للبناء السريع

### ✅ الحجم المتوقع
- APK: ~25-30 MB
- مساحة التثبيت: ~60-80 MB

## استكشاف الأخطاء

### مشكلة في البناء
```bash
flutter clean
flutter pub get
flutter build apk
```

### مشكلة في التشغيل
```bash
flutter devices
flutter run -d <device-id>
```

### تحديث التبعيات
```bash
flutter pub upgrade
```

## ملاحظات مهمة
- التطبيق يعمل بدون إنترنت بالكامل
- البيانات محفوظة محلياً
- يدعم الاتجاه RTL للعربية
- متوافق مع Android 5.0 وأحدث