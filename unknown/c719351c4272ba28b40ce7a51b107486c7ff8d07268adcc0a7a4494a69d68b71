import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'device_utils.dart';

/// Utility class for performance optimizations
class PerformanceUtils {
  /// Optimizes animations based on device capabilities
  static void optimizeAnimations() {
    // Adjust animation timings based on device performance
    if (DeviceUtils.isLowEndDevice()) {
      // Slightly speed up animations on low-end devices to reduce rendering load
      timeDilation = 0.8;
      
      // Improve performance on low-end devices
      SchedulerBinding.instance.addPostFrameCallback((_) {
        // Use a more conservative frame policy on low-end devices
        // This reduces the target frame rate slightly but improves stability
        SchedulerBinding.instance.resetEpoch();
      });
    } else {
      // Normal animation speed for high-end devices
      timeDilation = 1.0;
    }
  }
  
  /// Creates a widget that avoids unnecessary rebuilds
  static Widget optimizedBuilder({
    required Widget Function(BuildContext) builder,
    List<Object>? dependencies,
  }) {
    return dependencies == null
        ? Builder(builder: builder)
        : Builder(
            builder: (context) {
              for (final dependency in dependencies) {
                if (dependency is Listenable) {
                  return ListenableBuilder(
                    listenable: dependency,
                    builder: (context, _) => builder(context),
                  );
                }
              }
              return builder(context);
            },
          );
  }
  
  /// Schedules a task to run during idle time
  static void scheduleTask(VoidCallback task) {
    SchedulerBinding.instance.scheduleFrameCallback((_) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        task();
      });
    });
  }
  
  /// Optimizes image loading and caching
  static Widget optimizedImage(
    ImageProvider imageProvider, {
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    return Image(
      image: imageProvider,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      filterQuality: FilterQuality.medium,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }
        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
          child: child,
        );
      },
    );
  }
}