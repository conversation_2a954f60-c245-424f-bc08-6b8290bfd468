import 'package:flutter/material.dart';
import 'device_utils.dart';

/// Utility class for optimizing UI for low-end devices
class LowEndDeviceUI {
  /// Create a simplified version of a complex widget for low-end devices
  static Widget adaptiveWidget({
    required Widget normalWidget,
    required Widget simplifiedWidget,
  }) {
    return DeviceUtils.isLowEndDevice() ? simplifiedWidget : normalWidget;
  }
  
  /// Get appropriate animation duration for current device
  static Duration getAnimationDuration({
    required Duration normal,
    Duration? lowEnd,
  }) {
    if (DeviceUtils.isLowEndDevice()) {
      return lowEnd ?? Duration(milliseconds: normal.inMilliseconds ~/ 2);
    }
    return normal;
  }
  
  /// Get appropriate number of items to display based on device capability
  static int getOptimalItemCount({
    required int normalCount,
    int? lowEndCount,
  }) {
    if (DeviceUtils.isLowEndDevice()) {
      return lowEndCount ?? (normalCount ~/ 2).clamp(1, normalCount);
    }
    return normalCount;
  }
}