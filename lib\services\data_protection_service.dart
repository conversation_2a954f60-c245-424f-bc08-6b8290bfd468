import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'security_service.dart';

/// خدمة حماية البيانات المتقدمة
class DataProtectionService {
  static DataProtectionService? _instance;
  static DataProtectionService get instance => _instance ??= DataProtectionService._();
  DataProtectionService._();

  late final Encrypter _encrypter;
  late final IV _iv;
  late final Key _key;
  bool _isInitialized = false;

  /// تهيئة خدمة حماية البيانات
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      // إنشاء مفتاح تشفير قوي
      _key = Key.fromSecureRandom(32);
      _encrypter = Encrypter(AES(_key));
      _iv = IV.fromSecureRandom(16);

      _isInitialized = true;
      debugPrint('🔐 تم تهيئة خدمة حماية البيانات');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة حماية البيانات: $e');
      rethrow;
    }
  }

  /// تشفير البيانات الحساسة
  Future<String> encryptSensitiveData(Map<String, dynamic> data) async {
    try {
      if (!_isInitialized) await initialize();

      // إضافة طابع زمني وتوقيع
      final protectedData = {
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
        'signature': await _generateDataSignature(data),
        'checksum': _calculateChecksum(data),
      };

      final jsonString = jsonEncode(protectedData);
      final encrypted = _encrypter.encrypt(jsonString, iv: _iv);
      
      return encrypted.base64;
    } catch (e) {
      debugPrint('❌ خطأ في تشفير البيانات الحساسة: $e');
      rethrow;
    }
  }

  /// فك تشفير البيانات الحساسة
  Future<Map<String, dynamic>?> decryptSensitiveData(String encryptedData) async {
    try {
      if (!_isInitialized) await initialize();

      final encrypted = Encrypted.fromBase64(encryptedData);
      final decryptedJson = _encrypter.decrypt(encrypted, iv: _iv);
      final protectedData = jsonDecode(decryptedJson) as Map<String, dynamic>;

      // التحقق من التوقيع
      final data = protectedData['data'] as Map<String, dynamic>;
      final storedSignature = protectedData['signature'] as String;
      final expectedSignature = await _generateDataSignature(data);

      if (storedSignature != expectedSignature) {
        debugPrint('⚠️ فشل التحقق من توقيع البيانات');
        return null;
      }

      // التحقق من checksum
      final storedChecksum = protectedData['checksum'] as String;
      final expectedChecksum = _calculateChecksum(data);

      if (storedChecksum != expectedChecksum) {
        debugPrint('⚠️ فشل التحقق من checksum البيانات');
        return null;
      }

      return data;
    } catch (e) {
      debugPrint('❌ خطأ في فك تشفير البيانات الحساسة: $e');
      return null;
    }
  }

  /// تشفير كلمة المرور
  String hashPassword(String password, String salt) {
    try {
      final bytes = utf8.encode(password + salt);
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      debugPrint('❌ خطأ في تشفير كلمة المرور: $e');
      return password; // fallback
    }
  }

  /// إنشاء salt عشوائي
  String generateSalt() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  /// التحقق من كلمة المرور
  bool verifyPassword(String password, String hashedPassword, String salt) {
    try {
      final computedHash = hashPassword(password, salt);
      return computedHash == hashedPassword;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من كلمة المرور: $e');
      return false;
    }
  }

  /// تشفير ملف
  Future<Uint8List> encryptFile(Uint8List fileData) async {
    try {
      if (!_isInitialized) await initialize();

      // إضافة header للملف المشفر
      final header = {
        'version': '1.0',
        'algorithm': 'AES-256',
        'timestamp': DateTime.now().toIso8601String(),
      };

      final headerBytes = utf8.encode(jsonEncode(header));
      final headerLength = headerBytes.length;

      // تشفير البيانات
      final encrypted = _encrypter.encryptBytes(fileData, iv: _iv);
      
      // دمج الـ header مع البيانات المشفرة
      final result = Uint8List(4 + headerLength + encrypted.bytes.length);
      result.setRange(0, 4, _intToBytes(headerLength));
      result.setRange(4, 4 + headerLength, headerBytes);
      result.setRange(4 + headerLength, result.length, encrypted.bytes);

      return result;
    } catch (e) {
      debugPrint('❌ خطأ في تشفير الملف: $e');
      rethrow;
    }
  }

  /// فك تشفير ملف
  Future<Uint8List?> decryptFile(Uint8List encryptedData) async {
    try {
      if (!_isInitialized) await initialize();

      if (encryptedData.length < 4) {
        debugPrint('⚠️ ملف مشفر غير صالح');
        return null;
      }

      // قراءة طول الـ header
      final headerLength = _bytesToInt(encryptedData.sublist(0, 4));
      
      if (encryptedData.length < 4 + headerLength) {
        debugPrint('⚠️ ملف مشفر غير صالح');
        return null;
      }

      // قراءة الـ header
      final headerBytes = encryptedData.sublist(4, 4 + headerLength);
      final headerJson = utf8.decode(headerBytes);
      final header = jsonDecode(headerJson) as Map<String, dynamic>;

      // التحقق من الإصدار
      if (header['version'] != '1.0') {
        debugPrint('⚠️ إصدار غير مدعوم من الملف المشفر');
        return null;
      }

      // فك تشفير البيانات
      final encryptedBytes = encryptedData.sublist(4 + headerLength);
      final encrypted = Encrypted(encryptedBytes);
      final decrypted = _encrypter.decryptBytes(encrypted, iv: _iv);

      return Uint8List.fromList(decrypted);
    } catch (e) {
      debugPrint('❌ خطأ في فك تشفير الملف: $e');
      return null;
    }
  }

  /// حماية البيانات في الذاكرة
  void secureMemoryCleanup(List<dynamic> sensitiveData) {
    try {
      for (var data in sensitiveData) {
        if (data is String) {
          // محو البيانات النصية
          data = '0' * data.length;
        } else if (data is List<int>) {
          // محو البيانات الثنائية
          data.fillRange(0, data.length, 0);
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الذاكرة: $e');
    }
  }

  /// إنشاء نسخة احتياطية آمنة
  Future<String> createSecureBackup(Map<String, dynamic> data) async {
    try {
      // إضافة معلومات الحماية
      final secureData = {
        'data': data,
        'metadata': {
          'created_at': DateTime.now().toIso8601String(),
          'device_id': SecurityService.instance.deviceId,
          'version': '1.0',
          'integrity_hash': _calculateChecksum(data),
        }
      };

      return await encryptSensitiveData(secureData);
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء النسخة الاحتياطية الآمنة: $e');
      rethrow;
    }
  }

  /// استعادة النسخة الاحتياطية الآمنة
  Future<Map<String, dynamic>?> restoreSecureBackup(String encryptedBackup) async {
    try {
      final decryptedData = await decryptSensitiveData(encryptedBackup);
      
      if (decryptedData == null) {
        debugPrint('⚠️ فشل في فك تشفير النسخة الاحتياطية');
        return null;
      }

      final data = decryptedData['data'] as Map<String, dynamic>;
      final metadata = decryptedData['metadata'] as Map<String, dynamic>;

      // التحقق من تكامل البيانات
      final storedHash = metadata['integrity_hash'] as String;
      final computedHash = _calculateChecksum(data);

      if (storedHash != computedHash) {
        debugPrint('⚠️ فشل التحقق من تكامل النسخة الاحتياطية');
        return null;
      }

      return data;
    } catch (e) {
      debugPrint('❌ خطأ في استعادة النسخة الاحتياطية الآمنة: $e');
      return null;
    }
  }

  /// إنشاء توقيع للبيانات
  Future<String> _generateDataSignature(Map<String, dynamic> data) async {
    try {
      final jsonString = jsonEncode(data);
      final deviceId = SecurityService.instance.deviceId ?? 'unknown';
      final signatureData = '$jsonString:$deviceId';
      
      final bytes = utf8.encode(signatureData);
      final digest = sha256.convert(bytes);
      
      return digest.toString();
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء توقيع البيانات: $e');
      return 'fallback_signature';
    }
  }

  /// حساب checksum للبيانات
  String _calculateChecksum(Map<String, dynamic> data) {
    try {
      final jsonString = jsonEncode(data);
      final bytes = utf8.encode(jsonString);
      final digest = md5.convert(bytes);
      return digest.toString();
    } catch (e) {
      debugPrint('❌ خطأ في حساب checksum: $e');
      return 'fallback_checksum';
    }
  }

  /// تحويل int إلى bytes
  List<int> _intToBytes(int value) {
    return [
      (value >> 24) & 0xFF,
      (value >> 16) & 0xFF,
      (value >> 8) & 0xFF,
      value & 0xFF,
    ];
  }

  /// تحويل bytes إلى int
  int _bytesToInt(List<int> bytes) {
    return (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3];
  }

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;
}
