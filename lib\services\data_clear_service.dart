import 'data_service.dart';
import '../providers/app_provider.dart';

/// خدمة لحذف البيانات من التطبيق
class DataClearService {
  /// حذف جميع البيانات المخزنة
  static Future<void> clearAllData([AppProvider? provider]) async {
    await DataService.students.clear();
    await DataService.groups.clear();
    await DataService.lessons.clear();

    // إعادة تحميل البيانات في المزود إذا تم تمريره
    if (provider != null) {
      provider.loadData();
    }
  }

  /// حذف بيانات مجموعة معينة وطلابها ودروسها
  static Future<void> clearGroupData(String groupId) async {
    // حذف الطلاب المرتبطين بالمجموعة
    final studentsToDelete = DataService.students.values
        .where((student) => student.groupId == groupId)
        .map((student) => student.id)
        .toList();

    for (final studentId in studentsToDelete) {
      await DataService.students.delete(studentId);
    }

    // حذف الدروس المرتبطة بالمجموعة
    final lessonsToDelete = DataService.lessons.values
        .where((lesson) => lesson.groupId == groupId)
        .map((lesson) => lesson.id)
        .toList();

    for (final lessonId in lessonsToDelete) {
      await DataService.lessons.delete(lessonId);
    }

    // حذف المجموعة نفسها
    await DataService.groups.delete(groupId);
  }
}
