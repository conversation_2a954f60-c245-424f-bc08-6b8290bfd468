import '../utils/device_utils.dart';

/// A utility class for efficient caching of expensive computations and objects
class ComputeCache {
  static final ComputeCache _instance = ComputeCache._internal();
  factory ComputeCache() => _instance;
  ComputeCache._internal();
  
  // LRU cache for computed values
  final _cache = _LruCache<String, dynamic>(
    maxSize: DeviceUtils.isLowEndDevice() ? 50 : 200,
  );
  
  /// Get a cached value or compute it if not available
  T getOrCompute<T>(String key, T Function() compute) {
    if (_cache.containsKey(key)) {
      return _cache.get(key) as T;
    }
    
    final value = compute();
    _cache.put(key, value);
    return value;
  }
  
  /// Clear the entire cache
  void clear() {
    _cache.clear();
  }
}

/// An LRU (Least Recently Used) cache implementation
class _LruCache<K, V> {
  final int maxSize;
  final _map = <K, V>{};
  
  _LruCache({required this.maxSize});
  
  V? get(K key) {
    if (!_map.containsKey(key)) return null;
    
    // Move to end (most recently used)
    final value = _map.remove(key);
    _map[key] = value as V;
    return value;
  }
  
  void put(K key, V value) {
    if (_map.containsKey(key)) {
      _map.remove(key);
    } else if (_map.length >= maxSize) {
      // Remove oldest entry (first in map)
      _map.remove(_map.keys.first);
    }
    
    _map[key] = value;
  }
  
  bool containsKey(K key) => _map.containsKey(key);
  
  void clear() => _map.clear();
}