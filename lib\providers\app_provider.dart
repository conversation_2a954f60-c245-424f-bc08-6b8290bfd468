import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/student.dart';
import '../models/group.dart';
import '../models/lesson.dart';
import '../services/data_service.dart';
import '../services/security_service.dart';
import '../services/integrity_service.dart';
import '../services/data_protection_service.dart';

class AppProvider extends ChangeNotifier {
  bool _isOnline = false;
  List<Student> _students = [];
  List<Group> _groups = [];
  List<Lesson> _lessons = [];

  // Theme Management
  bool _isDarkMode = true;
  ThemeMode _themeMode = ThemeMode.dark;

  bool get isOnline => _isOnline;
  List<Student> get students => _students;
  List<Group> get groups => _groups;
  List<Lesson> get lessons => _lessons;

  // Theme Getters
  bool get isDarkMode => _isDarkMode;
  ThemeMode get themeMode => _themeMode;

  AppProvider() {
    _initConnectivity();
    _initData();
    _loadThemeSettings();
    _initSecurity();
  }

  void _initData() async {
    _loadData();
  }

  void _initConnectivity() async {
    final connectivity = Connectivity();
    final result = await connectivity.checkConnectivity();
    _isOnline = result != ConnectivityResult.none;

    connectivity.onConnectivityChanged.listen((result) {
      _isOnline = result != ConnectivityResult.none;
      notifyListeners();
    });
  }

  void loadData() {
    _students = DataService.students.values.toList();
    _groups = DataService.groups.values.toList();
    _lessons = DataService.lessons.values.toList();
    notifyListeners();
  }

  // للاستخدام الداخلي
  void _loadData() {
    loadData();
  }

  Future<void> addStudent(Student student) async {
    await DataService.addStudent(student);
    _loadData();
  }

  Future<void> addGroup(Group group) async {
    await DataService.addGroup(group);
    _loadData();
  }

  Future<void> addLesson(Lesson lesson) async {
    await DataService.addLesson(lesson);
    _loadData();
  }

  Future<void> updateStudent(Student student) async {
    await DataService.updateStudent(student);
    _loadData();
  }

  Future<void> updateGroup(Group group) async {
    await DataService.updateGroup(group);
    _loadData();
  }

  Future<void> updateLesson(Lesson lesson) async {
    await DataService.updateLesson(lesson);
    _loadData();
  }

  Future<void> deleteStudent(String id) async {
    await DataService.deleteStudent(id);
    _loadData();
  }

  Future<void> deleteGroup(String id) async {
    await DataService.deleteGroup(id);
    _loadData();
  }

  Future<void> deleteLesson(String id) async {
    await DataService.deleteLesson(id);
    _loadData();
  }

  List<Student> getStudentsByGroup(String groupId) {
    return _students.where((s) => s.groupId == groupId).toList();
  }

  List<Lesson> getTodayLessons() {
    final today = DateTime.now();
    return _lessons
        .where(
          (l) =>
              l.dateTime.year == today.year &&
              l.dateTime.month == today.month &&
              l.dateTime.day == today.day,
        )
        .toList();
  }

  List<Lesson> getTomorrowLessons() {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return _lessons
        .where(
          (l) =>
              l.dateTime.year == tomorrow.year &&
              l.dateTime.month == tomorrow.month &&
              l.dateTime.day == tomorrow.day,
        )
        .toList();
  }

  int get totalStudents => _students.length;
  int get totalGroups => _groups.length;
  int get completedLessonsToday =>
      getTodayLessons().where((l) => l.isCompleted).length;
  int get remainingLessonsToday =>
      getTodayLessons().where((l) => !l.isCompleted).length;

  // Theme Management Methods
  Future<void> _loadThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isDarkMode = prefs.getBool('isDarkMode') ?? true;
      _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;
      notifyListeners();
    } catch (e) {
      // استخدام القيم الافتراضية في حالة الخطأ
      _isDarkMode = true;
      _themeMode = ThemeMode.dark;
    }
  }

  Future<void> toggleTheme() async {
    try {
      _isDarkMode = !_isDarkMode;
      _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isDarkMode', _isDarkMode);

      notifyListeners();
    } catch (e) {
      // في حالة الخطأ، إرجاع القيمة السابقة
      _isDarkMode = !_isDarkMode;
      _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;
    }
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    try {
      _themeMode = mode;
      _isDarkMode = mode == ThemeMode.dark;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isDarkMode', _isDarkMode);

      notifyListeners();
    } catch (e) {
      // في حالة الخطأ، الاحتفاظ بالقيم الحالية
    }
  }

  // Security Management Methods
  Future<void> _initSecurity() async {
    try {
      // تهيئة خدمات الأمان
      await SecurityService.instance.initialize();
      await DataProtectionService.instance.initialize();

      // فحص دوري للتكامل
      await IntegrityService.instance.performPeriodicCheck();

      debugPrint('🔒 تم تهيئة نظام الأمان بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة نظام الأمان: $e');
    }
  }

  Future<bool> validateSecurity() async {
    try {
      // التحقق من صحة الجلسة
      final sessionValid = await SecurityService.instance.validateSession();

      // التحقق من حالة القفل
      final isLocked = await SecurityService.instance.isLocked();

      // التحقق من تكامل التطبيق
      final integrityValid = await IntegrityService.instance.verifyIntegrity();

      return sessionValid && !isLocked && integrityValid;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الأمان: $e');
      return false;
    }
  }

  Future<void> createSecureBackup() async {
    try {
      final data = {
        'students': _students
            .map(
              (s) => {
                'id': s.id,
                'name': s.name,
                'groupId': s.groupId,
                'isPresent': s.isPresent,
                'monthlyPayment': s.monthlyPayment,
                'hasPaid': s.hasPaid,
                'lastAttendance': s.lastAttendance.toIso8601String(),
              },
            )
            .toList(),
        'groups': _groups
            .map(
              (g) => {
                'id': g.id,
                'name': g.name,
                'subject': g.subject,
                'studentIds': g.studentIds,
              },
            )
            .toList(),
        'lessons': _lessons
            .map(
              (l) => {
                'id': l.id,
                'groupId': l.groupId,
                'dateTime': l.dateTime.toIso8601String(),
                'isCompleted': l.isCompleted,
                'attendedStudentIds': l.attendedStudentIds,
                'notes': l.notes,
              },
            )
            .toList(),
      };

      final encryptedBackup = await DataProtectionService.instance
          .createSecureBackup(data);

      // حفظ النسخة الاحتياطية المشفرة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('secure_backup', encryptedBackup);

      debugPrint('💾 تم إنشاء نسخة احتياطية آمنة');
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء النسخة الاحتياطية الآمنة: $e');
    }
  }

  Future<bool> restoreSecureBackup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedBackup = prefs.getString('secure_backup');

      if (encryptedBackup == null) {
        debugPrint('⚠️ لا توجد نسخة احتياطية آمنة');
        return false;
      }

      final data = await DataProtectionService.instance.restoreSecureBackup(
        encryptedBackup,
      );

      if (data == null) {
        debugPrint('❌ فشل في استعادة النسخة الاحتياطية الآمنة');
        return false;
      }

      // استعادة البيانات
      final studentsData = data['students'] as List?;
      final groupsData = data['groups'] as List?;
      final lessonsData = data['lessons'] as List?;

      if (studentsData != null) {
        _students = studentsData
            .map(
              (json) => Student(
                id: json['id'],
                name: json['name'],
                groupId: json['groupId'],
                isPresent: json['isPresent'],
                monthlyPayment: json['monthlyPayment'],
                hasPaid: json['hasPaid'],
                lastAttendance: DateTime.parse(json['lastAttendance']),
              ),
            )
            .toList();
      }

      if (groupsData != null) {
        _groups = groupsData
            .map(
              (json) => Group(
                id: json['id'],
                name: json['name'],
                subject: json['subject'],
                studentIds: List<String>.from(json['studentIds']),
              ),
            )
            .toList();
      }

      if (lessonsData != null) {
        _lessons = lessonsData
            .map(
              (json) => Lesson(
                id: json['id'],
                groupId: json['groupId'],
                dateTime: DateTime.parse(json['dateTime']),
                isCompleted: json['isCompleted'],
                attendedStudentIds: List<String>.from(
                  json['attendedStudentIds'],
                ),
                notes: json['notes'],
              ),
            )
            .toList();
      }

      notifyListeners();
      debugPrint('✅ تم استعادة النسخة الاحتياطية الآمنة بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في استعادة النسخة الاحتياطية الآمنة: $e');
      return false;
    }
  }
}
