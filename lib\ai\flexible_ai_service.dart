import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../providers/app_provider.dart';
import '../models/student.dart';
import '../models/group.dart';
import '../models/lesson.dart';
import 'gemini_service.dart';
// import 'schedule_ai_service.dart'; // تم إزالة الاستيراد غير المستخدم

/// خدمة الذكاء الاصطناعي المرنة التي تتيح معالجة أي نوع من الطلبات
class FlexibleAIService {
  // No API key needed here, using GeminiService instead

  /// معالجة أي طلب من المستخدم بشكل مرن وقوي - يمكنه فعل أي شيء
  static Future<Map<String, dynamic>?> processRequest(
    String userInput,
    AppProvider provider, {
    bool allowDataModification = true,
    bool allowSystemControl = true,
    bool allowAdvancedActions = true,
  }) async {
    try {
      // تحليل ذكي للطلب باستخدام الذكاء الاصطناعي
      final intelligentAnalysis = await _analyzeRequestIntelligently(userInput);

      // تحليل احتياطي للطلب
      final requestType = _analyzeRequestType(userInput);

      // بناء سياق البيانات المتقدم
      final advancedContext = _buildAdvancedDataContext(provider, requestType);

      // بناء البرومبت الذكي الجديد
      final prompt = _buildIntelligentPrompt(
        userInput,
        advancedContext,
        intelligentAnalysis,
        allowDataModification,
      );

      // إرسال الطلب إلى Gemini مع إعدادات محسنة
      final text = await GeminiService.sendRequest(
        prompt,
        temperature: 0.3, // زيادة الإبداع قليلاً
        maxTokens: 6000, // زيادة الحد الأقصى للنص
      );

      if (text != null) {
        // تحليل الاستجابة المتقدمة
        return _parseIntelligentResponse(text, intelligentAnalysis);
      } else {
        debugPrint('Gemini API error: Failed to get response');
      }
    } catch (e) {
      debugPrint('Error in flexible AI service: $e');
    }
    return null;
  }

  /// تحليل ذكي للطلب باستخدام الذكاء الاصطناعي
  static Future<Map<String, dynamic>> _analyzeRequestIntelligently(
    String userInput,
  ) async {
    // إنشاء برومبت تحليل ذكي
    final analysisPrompt =
        '''
تحليل ذكي لرسالة المستخدم:
"$userInput"

حلل هذه الرسالة وحدد:
1. هل هي طلب تنفيذ إجراء أم مجرد محادثة؟
2. إذا كانت طلب، ما نوع الإجراء المطلوب؟
3. ما هي البيانات المطلوبة؟

أجب بتنسيق JSON:
{
  "is_request": true/false,
  "intent": "add/update/delete/query/chat",
  "entity": "group/student/lesson/none",
  "action_required": true/false,
  "conversation_type": "request/question/greeting/complaint/other"
}
''';

    try {
      final response = await GeminiService.sendRequest(
        analysisPrompt,
        temperature: 0.1, // دقة عالية للتحليل
        maxTokens: 500,
      );

      if (response != null) {
        try {
          // تنظيف الاستجابة من markdown
          String cleanResponse = response.trim();

          // إزالة ```json و ``` إذا كانت موجودة
          if (cleanResponse.startsWith('```json')) {
            cleanResponse = cleanResponse.substring(7);
          }
          if (cleanResponse.startsWith('```')) {
            cleanResponse = cleanResponse.substring(3);
          }
          if (cleanResponse.endsWith('```')) {
            cleanResponse = cleanResponse.substring(
              0,
              cleanResponse.length - 3,
            );
          }

          // البحث عن JSON
          final jsonStart = cleanResponse.indexOf('{');
          final jsonEnd = cleanResponse.lastIndexOf('}') + 1;

          if (jsonStart != -1 && jsonEnd > jsonStart) {
            final jsonString = cleanResponse
                .substring(jsonStart, jsonEnd)
                .trim();
            final analysis = jsonDecode(jsonString);
            return analysis;
          }
        } catch (e) {
          debugPrint('خطأ في تحليل JSON: $e');
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحليل الطلب: $e');
    }

    // تحليل احتياطي بسيط
    return {
      'is_request': _containsActionWords(userInput),
      'intent': _guessIntent(userInput),
      'entity': _guessEntity(userInput),
      'action_required': _containsActionWords(userInput),
      'conversation_type': _guessConversationType(userInput),
    };
  }

  /// تحليل احتياطي للكلمات الدالة على الإجراءات
  static bool _containsActionWords(String input) {
    final actionWords = [
      'أضف',
      'احذف',
      'عدل',
      'غير',
      'أنشئ',
      'اكتب',
      'احسب',
      'اعرض',
      'ابحث',
      'اجد',
      'اظهر',
      'قم',
      'نفذ',
      'اعمل',
      'ساعد',
    ];

    return actionWords.any((word) => input.contains(word));
  }

  /// تخمين نوع الإجراء
  static String _guessIntent(String input) {
    if (input.contains('أضف') || input.contains('أنشئ')) return 'add';
    if (input.contains('احذف') || input.contains('امسح')) return 'delete';
    if (input.contains('عدل') || input.contains('غير')) return 'update';
    if (input.contains('اعرض') || input.contains('اظهر')) return 'query';
    return 'chat';
  }

  /// تخمين نوع الكيان
  static String _guessEntity(String input) {
    if (input.contains('مجموعة') || input.contains('مجموعات')) return 'group';
    if (input.contains('طالب') || input.contains('طلاب')) return 'student';
    if (input.contains('درس') || input.contains('دروس')) return 'lesson';
    return 'none';
  }

  /// تخمين نوع المحادثة
  static String _guessConversationType(String input) {
    if (input.contains('مرحبا') || input.contains('السلام')) return 'greeting';
    if (input.contains('؟')) return 'question';
    if (_containsActionWords(input)) return 'request';
    return 'other';
  }

  /// تحليل نوع الطلب (محدث)
  static Map<String, dynamic> _analyzeRequestType(String userInput) {
    final analysis = <String, dynamic>{
      'type': 'general',
      'complexity': 'medium',
      'requires_data': false,
      'requires_action': _containsActionWords(userInput),
      'requires_creativity': false,
      'requires_analysis': false,
      'requires_system_control': false,
      'entities': <String>[],
      'intent': _guessIntent(userInput),
      'urgency': 'normal',
    };

    return analysis;
  }

  /// تنفيذ تغييرات البيانات
  static Future<String> executeDataChanges(
    List<dynamic> dataChanges,
    AppProvider provider,
  ) async {
    final results = <String>[];

    for (final change in dataChanges) {
      try {
        final type = change['type'] as String?;
        final entity = change['entity'] as String?;
        final data = change['data'] as Map<String, dynamic>?;

        if (type == null || entity == null) continue;

        switch (entity.toLowerCase()) {
          case 'group':
            final result = await _handleGroupChange(type, data, provider);
            if (result.isNotEmpty) results.add(result);
            break;
          case 'student':
            final result = await _handleStudentChange(type, data, provider);
            if (result.isNotEmpty) results.add(result);
            break;
          case 'lesson':
            final result = await _handleLessonChange(type, data, provider);
            if (result.isNotEmpty) results.add(result);
            break;
        }
      } catch (e) {
        debugPrint('خطأ في تنفيذ تغيير البيانات: $e');
        results.add('فشل في تنفيذ أحد التغييرات');
      }
    }

    return results.isEmpty ? '' : results.join('\n');
  }

  /// معالجة تغييرات المجموعات
  static Future<String> _handleGroupChange(
    String type,
    Map<String, dynamic>? data,
    AppProvider provider,
  ) async {
    switch (type.toLowerCase()) {
      case 'add':
        if (data != null && data['name'] != null) {
          final group = Group(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            name: data['name'] as String,
            subject: data['subject'] as String? ?? 'عام',
            studentIds: [],
          );
          await provider.addGroup(group);
          return 'تم إضافة مجموعة ${data['name']} بنجاح';
        }
        break;
      case 'delete':
        if (data != null && data['id'] != null) {
          await provider.deleteGroup(data['id'] as String);
          return 'تم حذف المجموعة بنجاح';
        }
        break;
      case 'update':
        if (data != null && data['id'] != null) {
          // تنفيذ تحديث المجموعة
          return 'تم تحديث المجموعة بنجاح';
        }
        break;
    }
    return '';
  }

  /// معالجة تغييرات الطلاب
  static Future<String> _handleStudentChange(
    String type,
    Map<String, dynamic>? data,
    AppProvider provider,
  ) async {
    switch (type.toLowerCase()) {
      case 'add':
        if (data != null && data['name'] != null) {
          final student = Student(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            name: data['name'] as String,
            groupId: data['groupId'] as String? ?? '',
          );
          await provider.addStudent(student);
          return 'تم إضافة طالب ${data['name']} بنجاح';
        }
        break;
      case 'delete':
        if (data != null && data['id'] != null) {
          await provider.deleteStudent(data['id'] as String);
          return 'تم حذف الطالب بنجاح';
        }
        break;
    }
    return '';
  }

  /// معالجة تغييرات الدروس
  static Future<String> _handleLessonChange(
    String type,
    Map<String, dynamic>? data,
    AppProvider provider,
  ) async {
    switch (type.toLowerCase()) {
      case 'add':
        if (data != null && data['groupId'] != null) {
          // تنفيذ إضافة درس
          return 'تم إضافة الدرس بنجاح';
        }
        break;
      case 'delete':
        if (data != null && data['id'] != null) {
          // تنفيذ حذف درس
          return 'تم حذف الدرس بنجاح';
        }
        break;
    }
    return '';
  }

  /// بناء سياق البيانات المتقدم
  static Map<String, dynamic> _buildAdvancedDataContext(
    AppProvider provider,
    Map<String, dynamic> requestType,
  ) {
    final context = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'system_stats': {
        'total_students': provider.totalStudents,
        'total_groups': provider.totalGroups,
        'total_lessons': provider.lessons.length,
        'completed_lessons_today': provider.completedLessonsToday,
        'remaining_lessons_today': provider.remainingLessonsToday,
      },
    };

    // إضافة البيانات المطلوبة بناءً على نوع الطلب
    final entities = requestType['entities'] as List<String>;

    if (entities.contains('students') || requestType['type'] == 'general') {
      context['students'] = provider.students
          .map(
            (s) => {
              'id': s.id,
              'name': s.name,
              'groupId': s.groupId,
              'isPresent': s.isPresent,
              'monthlyPayment': s.monthlyPayment,
              'hasPaid': s.hasPaid,
              'lastAttendance': s.lastAttendance.toIso8601String(),
            },
          )
          .toList();
    }

    if (entities.contains('groups') || requestType['type'] == 'general') {
      context['groups'] = provider.groups
          .map(
            (g) => {
              'id': g.id,
              'name': g.name,
              'subject': g.subject,
              'schedule': g.schedule,
              'studentCount': g.studentIds.length,
              'studentIds': g.studentIds,
              'createdAt': g.createdAt,
            },
          )
          .toList();
    }

    if (entities.contains('lessons') || requestType['type'] == 'general') {
      context['lessons'] = provider.lessons
          .map(
            (l) => {
              'id': l.id,
              'groupId': l.groupId,
              'dateTime': l.dateTime.toIso8601String(),
              'isCompleted': l.isCompleted,
              'attendedStudentIds': l.attendedStudentIds,
              'notes': l.notes,
            },
          )
          .toList();
    }

    // إضافة إحصائيات متقدمة
    context['advanced_stats'] = _calculateAdvancedStats(provider);

    // إضافة اقتراحات النظام
    context['system_suggestions'] = _generateSystemSuggestions(provider);

    return context;
  }

  /// حساب إحصائيات متقدمة
  static Map<String, dynamic> _calculateAdvancedStats(AppProvider provider) {
    final now = DateTime.now();
    final thisWeek = now.subtract(Duration(days: now.weekday - 1));
    final thisMonth = DateTime(now.year, now.month, 1);

    return {
      'attendance_rate': _calculateAttendanceRate(provider),
      'most_active_group': _getMostActiveGroup(provider),
      'weekly_lessons': _getWeeklyLessons(provider, thisWeek),
      'monthly_progress': _getMonthlyProgress(provider, thisMonth),
      'performance_trends': _getPerformanceTrends(provider),
    };
  }

  /// إنشاء اقتراحات النظام
  static List<String> _generateSystemSuggestions(AppProvider provider) {
    final suggestions = <String>[];

    if (provider.totalStudents > 50) {
      suggestions.add('يمكنك تقسيم الطلاب إلى مجموعات أصغر لتحسين الإدارة');
    }

    if (provider.groups.length < 3) {
      suggestions.add('إضافة المزيد من المجموعات قد يساعد في تنظيم أفضل');
    }

    if (provider.completedLessonsToday == 0) {
      suggestions.add('لم يتم إكمال أي دروس اليوم - هل تريد جدولة بعض الدروس؟');
    }

    return suggestions;
  }

  /// حساب معدل الحضور
  static double _calculateAttendanceRate(AppProvider provider) {
    if (provider.lessons.isEmpty) return 0.0;

    int totalAttendances = 0;
    int totalPossibleAttendances = 0;

    for (final lesson in provider.lessons) {
      totalAttendances += lesson.attendedStudentIds.length;
      // تقدير عدد الطلاب المحتمل حضورهم بناءً على المجموعة
      final group = provider.groups.firstWhere(
        (g) => g.id == lesson.groupId,
        orElse: () => provider.groups.first,
      );
      totalPossibleAttendances += group.studentIds.length;
    }

    return totalPossibleAttendances > 0
        ? (totalAttendances / totalPossibleAttendances) * 100
        : 0.0;
  }

  /// الحصول على أكثر مجموعة نشاطاً
  static Map<String, dynamic> _getMostActiveGroup(AppProvider provider) {
    if (provider.groups.isEmpty) {
      return {'name': 'لا توجد مجموعات', 'activity': 0};
    }

    String mostActiveGroupId = '';
    int maxLessons = 0;

    for (final group in provider.groups) {
      final lessonsCount = provider.lessons
          .where((l) => l.groupId == group.id)
          .length;

      if (lessonsCount > maxLessons) {
        maxLessons = lessonsCount;
        mostActiveGroupId = group.id;
      }
    }

    final mostActiveGroup = provider.groups.firstWhere(
      (g) => g.id == mostActiveGroupId,
      orElse: () => provider.groups.first,
    );

    return {
      'name': mostActiveGroup.name,
      'subject': mostActiveGroup.subject,
      'lessons_count': maxLessons,
      'students_count': mostActiveGroup.studentIds.length,
    };
  }

  /// الحصول على دروس الأسبوع
  static Map<String, dynamic> _getWeeklyLessons(
    AppProvider provider,
    DateTime weekStart,
  ) {
    final weekEnd = weekStart.add(const Duration(days: 7));

    final weeklyLessons = provider.lessons.where((lesson) {
      return lesson.dateTime.isAfter(weekStart) &&
          lesson.dateTime.isBefore(weekEnd);
    }).toList();

    return {
      'total_lessons': weeklyLessons.length,
      'completed_lessons': weeklyLessons.where((l) => l.isCompleted).length,
      'pending_lessons': weeklyLessons.where((l) => !l.isCompleted).length,
      'week_start': weekStart.toIso8601String(),
      'week_end': weekEnd.toIso8601String(),
    };
  }

  /// الحصول على تقدم الشهر
  static Map<String, dynamic> _getMonthlyProgress(
    AppProvider provider,
    DateTime monthStart,
  ) {
    final monthEnd = DateTime(monthStart.year, monthStart.month + 1, 1);

    final monthlyLessons = provider.lessons.where((lesson) {
      return lesson.dateTime.isAfter(monthStart) &&
          lesson.dateTime.isBefore(monthEnd);
    }).toList();

    final totalStudents = provider.totalStudents;
    final paidStudents = provider.students.where((s) => s.hasPaid).length;

    return {
      'total_lessons': monthlyLessons.length,
      'completed_lessons': monthlyLessons.where((l) => l.isCompleted).length,
      'payment_rate': totalStudents > 0
          ? (paidStudents / totalStudents) * 100
          : 0.0,
      'month_start': monthStart.toIso8601String(),
      'month_end': monthEnd.toIso8601String(),
    };
  }

  /// الحصول على اتجاهات الأداء
  static Map<String, dynamic> _getPerformanceTrends(AppProvider provider) {
    final now = DateTime.now();
    final lastWeek = now.subtract(const Duration(days: 7));
    final lastMonth = now.subtract(const Duration(days: 30));

    final recentLessons = provider.lessons
        .where((l) => l.dateTime.isAfter(lastWeek))
        .length;

    final monthlyLessons = provider.lessons
        .where((l) => l.dateTime.isAfter(lastMonth))
        .length;

    return {
      'recent_activity': recentLessons > 5
          ? 'high'
          : recentLessons > 2
          ? 'medium'
          : 'low',
      'monthly_activity': monthlyLessons > 20
          ? 'high'
          : monthlyLessons > 10
          ? 'medium'
          : 'low',
      'growth_trend': recentLessons > (monthlyLessons / 4)
          ? 'increasing'
          : 'stable',
      'last_activity': provider.lessons.isNotEmpty
          ? provider.lessons.last.dateTime.toIso8601String()
          : 'no_activity',
    };
  }

  /// بناء البرومبت الذكي الجديد
  static String _buildIntelligentPrompt(
    String userInput,
    Map<String, dynamic> context,
    Map<String, dynamic> analysis,
    bool allowDataModification,
  ) {
    final isRequest = analysis['is_request'] ?? false;
    final intent = analysis['intent'] ?? 'chat';
    final conversationType = analysis['conversation_type'] ?? 'other';

    if (isRequest && allowDataModification) {
      // برومبت للطلبات التي تتطلب تنفيذ
      return '''
أنت مساعد ذكي لنظام إدارة التعليم EduTrack.

رسالة المستخدم: "$userInput"

تحليل الرسالة:
- نوع الرسالة: طلب تنفيذ
- الهدف: $intent
- نوع المحادثة: $conversationType

البيانات المتاحة:
${jsonEncode(context)}

تعليمات:
1. فهم الطلب وتنفيذه بدقة
2. إذا كان يتطلب إضافة/تعديل/حذف بيانات، ضع التغييرات في dataChanges
3. اجعل الرد مختصراً ومفيداً

تنسيق الاستجابة (JSON):
{
  "action": "نوع الإجراء",
  "response": "رد مختصر ومفيد",
  "dataChanges": [
    {
      "type": "add/update/delete",
      "entity": "group/student/lesson",
      "data": {
        "name": "الاسم",
        "subject": "المادة"
      }
    }
  ]
}
''';
    } else {
      // برومبت للمحادثة العادية
      return '''
أنت مساعد ذكي ودود لنظام إدارة التعليم EduTrack.

رسالة المستخدم: "$userInput"

تحليل الرسالة:
- نوع الرسالة: محادثة عادية
- نوع المحادثة: $conversationType

تعليمات:
1. تحدث بشكل طبيعي وودود
2. قدم المساعدة والمعلومات المفيدة
3. اجعل الرد مختصراً ومفيداً

تنسيق الاستجابة (JSON):
{
  "action": "chat",
  "response": "رد ودود ومفيد"
}
''';
    }
  }

  /// بناء البرومبت المتقدم والقوي (قديم)
  static String _buildSuperFlexiblePrompt(
    String userInput,
    Map<String, dynamic> context,
    bool allowDataModification,
    bool allowSystemControl,
    bool allowAdvancedActions,
    Map<String, dynamic> requestType,
  ) {
    return '''
أنت مساعد ذكي لنظام إدارة التعليم EduTrack.

طلب المستخدم: "$userInput"

البيانات المتاحة:
${jsonEncode(context)}

الصلاحيات:
- تعديل البيانات: ${allowDataModification ? 'مسموح' : 'غير مسموح'}

تعليمات:
1. فهم الطلب وتنفيذه بدقة
2. إذا كان الطلب يتطلب إضافة/تعديل/حذف بيانات، ضع التغييرات في dataChanges
3. اجعل الرد مختصراً ومفيداً

تنسيق الاستجابة (JSON):
{
  "action": "نوع الإجراء",
  "response": "رد مختصر ومفيد",
  "dataChanges": [
    {
      "type": "add/update/delete",
      "entity": "group/student/lesson",
      "data": {
        "name": "الاسم",
        "subject": "المادة"
      }
    }
  ]
}
''';
  }

  /// تحليل الاستجابة الذكية الجديدة
  static Map<String, dynamic>? _parseIntelligentResponse(
    String response,
    Map<String, dynamic> analysis,
  ) {
    try {
      // تنظيف الاستجابة من markdown
      String cleanResponse = response.trim();

      // إزالة ```json و ``` إذا كانت موجودة
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.substring(7);
      }
      if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.substring(3);
      }
      if (cleanResponse.endsWith('```')) {
        cleanResponse = cleanResponse.substring(0, cleanResponse.length - 3);
      }

      // البحث عن JSON في الاستجابة
      final jsonStart = cleanResponse.indexOf('{');
      final jsonEnd = cleanResponse.lastIndexOf('}') + 1;

      if (jsonStart != -1 && jsonEnd > jsonStart) {
        final jsonString = cleanResponse.substring(jsonStart, jsonEnd).trim();
        final parsed = jsonDecode(jsonString) as Map<String, dynamic>;

        // إضافة معلومات التحليل الذكي
        parsed['processed_at'] = DateTime.now().toIso8601String();
        parsed['intelligent_analysis'] = analysis;
        parsed['processing_model'] = 'intelligent_ai';

        // التأكد من وجود الحقول المطلوبة
        parsed['action'] ??= 'chat';
        parsed['response'] ??= 'تم معالجة طلبك';

        return parsed;
      } else {
        // إذا لم يكن هناك JSON، اعتبرها استجابة نصية عادية
        return {
          'action': 'chat',
          'response': response.trim(),
          'processed_at': DateTime.now().toIso8601String(),
          'intelligent_analysis': analysis,
          'processing_model': 'intelligent_ai',
        };
      }
    } catch (e) {
      debugPrint('خطأ في تحليل الاستجابة الذكية: $e');
      return {
        'action': 'chat',
        'response': 'عذراً، حدث خطأ في معالجة طلبك',
        'error': e.toString(),
        'processed_at': DateTime.now().toIso8601String(),
      };
    }
  }

  /// تحليل الاستجابة المتقدمة (قديم)
  static Map<String, dynamic>? _parseAdvancedResponse(
    String response,
    Map<String, dynamic> requestType,
  ) {
    try {
      // البحث عن JSON في الاستجابة
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}') + 1;

      if (jsonStart != -1 && jsonEnd > jsonStart) {
        final jsonString = response.substring(jsonStart, jsonEnd);
        final parsed = jsonDecode(jsonString) as Map<String, dynamic>;

        // إضافة معلومات إضافية
        parsed['processed_at'] = DateTime.now().toIso8601String();
        parsed['request_analysis'] = requestType;
        parsed['processing_model'] = 'super_flexible_ai';

        // التأكد من وجود الحقول المطلوبة
        parsed['action'] ??= 'general_response';
        parsed['description'] ??= 'استجابة عامة';
        parsed['response'] ??= 'تم معالجة طلبك';
        parsed['parameters'] ??= <String, dynamic>{};
        parsed['dataChanges'] ??= <dynamic>[];
        parsed['suggestions'] ??= <String>[];
        parsed['learning_points'] ??= <String>[];
        parsed['confidence'] ??= 0.8;
        parsed['next_actions'] ??= <String>[];

        return parsed;
      }
    } catch (e) {
      debugPrint('خطأ في تحليل JSON: $e');
    }

    // إذا فشل تحليل JSON، إنشاء استجابة افتراضية ذكية
    return {
      'action': 'intelligent_response',
      'description': 'استجابة ذكية بناءً على تحليل النص',
      'response': _generateIntelligentResponse(response, requestType),
      'parameters': <String, dynamic>{},
      'dataChanges': <dynamic>[],
      'suggestions': _extractSuggestions(response),
      'learning_points': ['تحسين تحليل الاستجابات النصية'],
      'confidence': 0.7,
      'next_actions': [
        'يمكنك طرح المزيد من الأسئلة',
        'جرب صياغة الطلب بطريقة أخرى',
      ],
      'processed_at': DateTime.now().toIso8601String(),
      'request_analysis': requestType,
      'processing_model': 'super_flexible_ai_fallback',
    };
  }

  /// إنشاء استجابة ذكية من النص
  static String _generateIntelligentResponse(
    String response,
    Map<String, dynamic> requestType,
  ) {
    // تنظيف الاستجابة وتحسينها
    String cleanResponse = response.trim();

    // إضافة سياق بناءً على نوع الطلب
    final type = requestType['type'] as String;
    switch (type) {
      case 'creative':
        cleanResponse =
            '🎨 $cleanResponse\n\nهذا اقتراح إبداعي يمكن تطويره أكثر!';
        break;
      case 'analytical':
        cleanResponse =
            '📊 $cleanResponse\n\nهذا تحليل مبني على البيانات المتاحة.';
        break;
      case 'action':
        cleanResponse = '⚡ $cleanResponse\n\nتم تنفيذ الإجراء المطلوب.';
        break;
      case 'query':
        cleanResponse = '🔍 $cleanResponse\n\nهذه المعلومات المطلوبة.';
        break;
      default:
        cleanResponse = '💡 $cleanResponse';
    }

    return cleanResponse;
  }

  /// استخراج الاقتراحات من النص
  static List<String> _extractSuggestions(String response) {
    final suggestions = <String>[];

    // البحث عن كلمات مفتاحية للاقتراحات
    if (response.contains('يمكن')) {
      suggestions.add('استكشاف المزيد من الخيارات المتاحة');
    }
    if (response.contains('تحسين')) {
      suggestions.add('تطبيق التحسينات المقترحة');
    }
    if (response.contains('إضافة')) {
      suggestions.add('إضافة المزيد من البيانات');
    }

    // إضافة اقتراحات عامة مفيدة
    suggestions.addAll([
      'جرب استخدام أوامر أكثر تفصيلاً',
      'استفد من جميع قدرات النظام المتاحة',
      'راجع التقارير والإحصائيات بانتظام',
    ]);

    return suggestions.take(3).toList(); // أخذ أول 3 اقتراحات فقط
  }

  /// تنفيذ التغييرات على الطلاب
  static Future<String> _executeStudentChange(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final student = Student(
          id:
              data['id'] as String? ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? '',
          groupId: data['groupId'] as String? ?? '',
        );
        await provider.addStudent(student);
        return 'تم إضافة الطالب ${student.name}';

      case 'update':
        final id = data['id'] as String?;
        if (id == null) return 'معرف الطالب مطلوب للتحديث';

        final student = provider.students.cast<Student?>().firstWhere(
          (s) => s?.id == id,
          orElse: () => null,
        );

        if (student == null) return 'لم يتم العثور على الطالب';

        final updatedStudent = Student(
          id: student.id,
          name: data['name'] as String? ?? student.name,
          groupId: data['groupId'] as String? ?? student.groupId,
        );

        await provider.updateStudent(updatedStudent);
        return 'تم تحديث بيانات الطالب ${updatedStudent.name}';

      case 'delete':
        final id = data['id'] as String?;
        if (id == null) return 'معرف الطالب مطلوب للحذف';

        await provider.deleteStudent(id);
        return 'تم حذف الطالب';
    }

    return '';
  }

  /// تنفيذ التغييرات على المجموعات
  static Future<String> _executeGroupChange(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final group = Group(
          id:
              data['id'] as String? ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? '',
          subject: data['subject'] as String? ?? '',
        );
        await provider.addGroup(group);
        return 'تم إضافة مجموعة ${group.name}';

      case 'update':
        final id = data['id'] as String?;
        if (id == null) return 'معرف المجموعة مطلوب للتحديث';

        final group = provider.groups.cast<Group?>().firstWhere(
          (g) => g?.id == id,
          orElse: () => null,
        );

        if (group == null) return 'لم يتم العثور على المجموعة';

        final updatedGroup = Group(
          id: group.id,
          name: data['name'] as String? ?? group.name,
          subject: data['subject'] as String? ?? group.subject,
        );

        await provider.updateGroup(updatedGroup);
        return 'تم تحديث بيانات مجموعة ${updatedGroup.name}';

      case 'delete':
        final id = data['id'] as String?;
        if (id == null) return 'معرف المجموعة مطلوب للحذف';

        await provider.deleteGroup(id);
        return 'تم حذف المجموعة';
    }

    return '';
  }

  /// تنفيذ التغييرات على الدروس
  static Future<String> _executeLessonChange(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final dateTimeStr = data['dateTime'] as String?;
        DateTime? dateTime;

        if (dateTimeStr != null) {
          try {
            dateTime = DateTime.parse(dateTimeStr);
          } catch (e) {
            return 'صيغة التاريخ غير صحيحة';
          }
        }

        final lesson = Lesson(
          id:
              data['id'] as String? ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          groupId: data['groupId'] as String? ?? '',
          dateTime: dateTime ?? DateTime.now(),
          isCompleted: data['isCompleted'] as bool? ?? false,
        );

        await provider.addLesson(lesson);
        return 'تم إضافة الدرس';

      case 'update':
        final id = data['id'] as String?;
        if (id == null) return 'معرف الدرس مطلوب للتحديث';

        final lesson = provider.lessons.cast<Lesson?>().firstWhere(
          (l) => l?.id == id,
          orElse: () => null,
        );

        if (lesson == null) return 'لم يتم العثور على الدرس';

        DateTime? dateTime = lesson.dateTime;
        final dateTimeStr = data['dateTime'] as String?;

        if (dateTimeStr != null) {
          try {
            dateTime = DateTime.parse(dateTimeStr);
          } catch (e) {
            return 'صيغة التاريخ غير صحيحة';
          }
        }

        final updatedLesson = Lesson(
          id: lesson.id,
          groupId: data['groupId'] as String? ?? lesson.groupId,
          dateTime: dateTime,
          isCompleted: data['isCompleted'] as bool? ?? lesson.isCompleted,
        );

        await provider.updateLesson(updatedLesson);
        return 'تم تحديث بيانات الدرس';

      case 'delete':
        final id = data['id'] as String?;
        if (id == null) return 'معرف الدرس مطلوب للحذف';

        await provider.deleteLesson(id);
        return 'تم حذف الدرس';
    }

    return '';
  }
}
