import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../providers/app_provider.dart';
import '../models/student.dart';
import '../models/group.dart';
import '../models/lesson.dart';
import 'gemini_service.dart';
// import 'schedule_ai_service.dart'; // تم إزالة الاستيراد غير المستخدم

/// خدمة الذكاء الاصطناعي المرنة التي تتيح معالجة أي نوع من الطلبات
class FlexibleAIService {
  // No API key needed here, using GeminiService instead

  /// معالجة أي طلب من المستخدم بشكل مرن وقوي - يمكنه فعل أي شيء
  static Future<Map<String, dynamic>?> processRequest(
    String userInput,
    AppProvider provider, {
    bool allowDataModification = true,
    bool allowSystemControl = true,
    bool allowAdvancedActions = true,
  }) async {
    try {
      // تحليل نوع الطلب أولاً
      final requestType = _analyzeRequestType(userInput);

      // بناء سياق البيانات المتقدم
      final advancedContext = _buildAdvancedDataContext(provider, requestType);

      // بناء البرومبت المتقدم والقوي
      final prompt = _buildSuperFlexiblePrompt(
        userInput,
        advancedContext,
        allowDataModification,
        allowSystemControl,
        allowAdvancedActions,
        requestType,
      );

      // إرسال الطلب إلى Gemini مع إعدادات محسنة
      final text = await GeminiService.sendRequest(
        prompt,
        temperature: 0.3, // زيادة الإبداع قليلاً
        maxTokens: 6000, // زيادة الحد الأقصى للنص
      );

      if (text != null) {
        // تحليل الاستجابة المتقدمة
        return _parseAdvancedResponse(text, requestType);
      } else {
        debugPrint('Gemini API error: Failed to get response');
      }
    } catch (e) {
      debugPrint('Error in flexible AI service: $e');
    }
    return null;
  }

  /// تحليل نوع الطلب لتحديد أفضل طريقة للمعالجة
  static Map<String, dynamic> _analyzeRequestType(String userInput) {
    final input = userInput.toLowerCase();
    final analysis = <String, dynamic>{
      'type': 'general',
      'complexity': 'medium',
      'requires_data': false,
      'requires_action': false,
      'requires_creativity': false,
      'requires_analysis': false,
      'requires_system_control': false,
      'entities': <String>[],
      'intent': 'unknown',
      'urgency': 'normal',
    };

    // تحديد نوع الطلب
    if (input.contains('أنشئ') ||
        input.contains('اكتب') ||
        input.contains('صمم') ||
        input.contains('ابتكر') ||
        input.contains('طور')) {
      analysis['type'] = 'creative';
      analysis['requires_creativity'] = true;
      analysis['complexity'] = 'high';
    } else if (input.contains('حلل') ||
        input.contains('احسب') ||
        input.contains('قارن') ||
        input.contains('ادرس') ||
        input.contains('فحص')) {
      analysis['type'] = 'analytical';
      analysis['requires_analysis'] = true;
      analysis['requires_data'] = true;
    } else if (input.contains('أضف') ||
        input.contains('احذف') ||
        input.contains('عدل') ||
        input.contains('غير') ||
        input.contains('حدث')) {
      analysis['type'] = 'action';
      analysis['requires_action'] = true;
      analysis['requires_data'] = true;
    } else if (input.contains('ابحث') ||
        input.contains('اعرض') ||
        input.contains('أظهر') ||
        input.contains('اجلب') ||
        input.contains('استخرج')) {
      analysis['type'] = 'query';
      analysis['requires_data'] = true;
    } else if (input.contains('تحكم') ||
        input.contains('شغل') ||
        input.contains('أوقف') ||
        input.contains('إعادة تشغيل') ||
        input.contains('إعدادات')) {
      analysis['type'] = 'system_control';
      analysis['requires_system_control'] = true;
    }

    // تحديد الكيانات المطلوبة
    final entities = <String>[];
    if (input.contains('طالب') || input.contains('طلاب')) {
      entities.add('students');
    }
    if (input.contains('مجموعة') || input.contains('مجموعات')) {
      entities.add('groups');
    }
    if (input.contains('درس') ||
        input.contains('دروس') ||
        input.contains('حصة')) {
      entities.add('lessons');
    }
    if (input.contains('حضور') || input.contains('غياب')) {
      entities.add('attendance');
    }
    if (input.contains('جدول') ||
        input.contains('موعد') ||
        input.contains('وقت')) {
      entities.add('schedule');
    }
    if (input.contains('تقرير') || input.contains('إحصائية')) {
      entities.add('reports');
    }
    if (input.contains('رسوم') ||
        input.contains('مال') ||
        input.contains('دفع')) {
      entities.add('fees');
    }
    analysis['entities'] = entities;

    // تحديد مستوى التعقيد
    if (input.length > 100 || input.split(' ').length > 20) {
      analysis['complexity'] = 'high';
    } else if (input.length < 20) {
      analysis['complexity'] = 'low';
    }

    // تحديد الهدف
    if (input.contains('مساعدة') ||
        input.contains('كيف') ||
        input.contains('ماذا')) {
      analysis['intent'] = 'help';
    } else if (input.contains('تقرير') ||
        input.contains('إحصائية') ||
        input.contains('ملخص')) {
      analysis['intent'] = 'report';
    } else if (input.contains('تحسين') ||
        input.contains('تطوير') ||
        input.contains('تحسن')) {
      analysis['intent'] = 'optimization';
    } else if (input.contains('مشكلة') ||
        input.contains('خطأ') ||
        input.contains('عطل')) {
      analysis['intent'] = 'troubleshooting';
    }

    // تحديد الأولوية
    if (input.contains('عاجل') ||
        input.contains('سريع') ||
        input.contains('فوري')) {
      analysis['urgency'] = 'high';
    } else if (input.contains('لاحقاً') || input.contains('متى تستطيع')) {
      analysis['urgency'] = 'low';
    }

    return analysis;
  }

  /// بناء سياق البيانات المتقدم
  static Map<String, dynamic> _buildAdvancedDataContext(
    AppProvider provider,
    Map<String, dynamic> requestType,
  ) {
    final context = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'system_stats': {
        'total_students': provider.totalStudents,
        'total_groups': provider.totalGroups,
        'total_lessons': provider.lessons.length,
        'completed_lessons_today': provider.completedLessonsToday,
        'remaining_lessons_today': provider.remainingLessonsToday,
      },
    };

    // إضافة البيانات المطلوبة بناءً على نوع الطلب
    final entities = requestType['entities'] as List<String>;

    if (entities.contains('students') || requestType['type'] == 'general') {
      context['students'] = provider.students
          .map(
            (s) => {
              'id': s.id,
              'name': s.name,
              'groupId': s.groupId,
              'isPresent': s.isPresent,
              'monthlyPayment': s.monthlyPayment,
              'hasPaid': s.hasPaid,
              'lastAttendance': s.lastAttendance.toIso8601String(),
            },
          )
          .toList();
    }

    if (entities.contains('groups') || requestType['type'] == 'general') {
      context['groups'] = provider.groups
          .map(
            (g) => {
              'id': g.id,
              'name': g.name,
              'subject': g.subject,
              'schedule': g.schedule,
              'studentCount': g.studentIds.length,
              'studentIds': g.studentIds,
              'createdAt': g.createdAt,
            },
          )
          .toList();
    }

    if (entities.contains('lessons') || requestType['type'] == 'general') {
      context['lessons'] = provider.lessons
          .map(
            (l) => {
              'id': l.id,
              'groupId': l.groupId,
              'dateTime': l.dateTime.toIso8601String(),
              'isCompleted': l.isCompleted,
              'attendedStudentIds': l.attendedStudentIds,
              'notes': l.notes,
            },
          )
          .toList();
    }

    // إضافة إحصائيات متقدمة
    context['advanced_stats'] = _calculateAdvancedStats(provider);

    // إضافة اقتراحات النظام
    context['system_suggestions'] = _generateSystemSuggestions(provider);

    return context;
  }

  /// حساب إحصائيات متقدمة
  static Map<String, dynamic> _calculateAdvancedStats(AppProvider provider) {
    final now = DateTime.now();
    final thisWeek = now.subtract(Duration(days: now.weekday - 1));
    final thisMonth = DateTime(now.year, now.month, 1);

    return {
      'attendance_rate': _calculateAttendanceRate(provider),
      'most_active_group': _getMostActiveGroup(provider),
      'weekly_lessons': _getWeeklyLessons(provider, thisWeek),
      'monthly_progress': _getMonthlyProgress(provider, thisMonth),
      'performance_trends': _getPerformanceTrends(provider),
    };
  }

  /// إنشاء اقتراحات النظام
  static List<String> _generateSystemSuggestions(AppProvider provider) {
    final suggestions = <String>[];

    if (provider.totalStudents > 50) {
      suggestions.add('يمكنك تقسيم الطلاب إلى مجموعات أصغر لتحسين الإدارة');
    }

    if (provider.groups.length < 3) {
      suggestions.add('إضافة المزيد من المجموعات قد يساعد في تنظيم أفضل');
    }

    if (provider.completedLessonsToday == 0) {
      suggestions.add('لم يتم إكمال أي دروس اليوم - هل تريد جدولة بعض الدروس؟');
    }

    return suggestions;
  }

  /// حساب معدل الحضور
  static double _calculateAttendanceRate(AppProvider provider) {
    if (provider.lessons.isEmpty) return 0.0;

    int totalAttendances = 0;
    int totalPossibleAttendances = 0;

    for (final lesson in provider.lessons) {
      totalAttendances += lesson.attendedStudentIds.length;
      // تقدير عدد الطلاب المحتمل حضورهم بناءً على المجموعة
      final group = provider.groups.firstWhere(
        (g) => g.id == lesson.groupId,
        orElse: () => provider.groups.first,
      );
      totalPossibleAttendances += group.studentIds.length;
    }

    return totalPossibleAttendances > 0
        ? (totalAttendances / totalPossibleAttendances) * 100
        : 0.0;
  }

  /// الحصول على أكثر مجموعة نشاطاً
  static Map<String, dynamic> _getMostActiveGroup(AppProvider provider) {
    if (provider.groups.isEmpty) {
      return {'name': 'لا توجد مجموعات', 'activity': 0};
    }

    String mostActiveGroupId = '';
    int maxLessons = 0;

    for (final group in provider.groups) {
      final lessonsCount = provider.lessons
          .where((l) => l.groupId == group.id)
          .length;

      if (lessonsCount > maxLessons) {
        maxLessons = lessonsCount;
        mostActiveGroupId = group.id;
      }
    }

    final mostActiveGroup = provider.groups.firstWhere(
      (g) => g.id == mostActiveGroupId,
      orElse: () => provider.groups.first,
    );

    return {
      'name': mostActiveGroup.name,
      'subject': mostActiveGroup.subject,
      'lessons_count': maxLessons,
      'students_count': mostActiveGroup.studentIds.length,
    };
  }

  /// الحصول على دروس الأسبوع
  static Map<String, dynamic> _getWeeklyLessons(
    AppProvider provider,
    DateTime weekStart,
  ) {
    final weekEnd = weekStart.add(const Duration(days: 7));

    final weeklyLessons = provider.lessons.where((lesson) {
      return lesson.dateTime.isAfter(weekStart) &&
          lesson.dateTime.isBefore(weekEnd);
    }).toList();

    return {
      'total_lessons': weeklyLessons.length,
      'completed_lessons': weeklyLessons.where((l) => l.isCompleted).length,
      'pending_lessons': weeklyLessons.where((l) => !l.isCompleted).length,
      'week_start': weekStart.toIso8601String(),
      'week_end': weekEnd.toIso8601String(),
    };
  }

  /// الحصول على تقدم الشهر
  static Map<String, dynamic> _getMonthlyProgress(
    AppProvider provider,
    DateTime monthStart,
  ) {
    final monthEnd = DateTime(monthStart.year, monthStart.month + 1, 1);

    final monthlyLessons = provider.lessons.where((lesson) {
      return lesson.dateTime.isAfter(monthStart) &&
          lesson.dateTime.isBefore(monthEnd);
    }).toList();

    final totalStudents = provider.totalStudents;
    final paidStudents = provider.students.where((s) => s.hasPaid).length;

    return {
      'total_lessons': monthlyLessons.length,
      'completed_lessons': monthlyLessons.where((l) => l.isCompleted).length,
      'payment_rate': totalStudents > 0
          ? (paidStudents / totalStudents) * 100
          : 0.0,
      'month_start': monthStart.toIso8601String(),
      'month_end': monthEnd.toIso8601String(),
    };
  }

  /// الحصول على اتجاهات الأداء
  static Map<String, dynamic> _getPerformanceTrends(AppProvider provider) {
    final now = DateTime.now();
    final lastWeek = now.subtract(const Duration(days: 7));
    final lastMonth = now.subtract(const Duration(days: 30));

    final recentLessons = provider.lessons
        .where((l) => l.dateTime.isAfter(lastWeek))
        .length;

    final monthlyLessons = provider.lessons
        .where((l) => l.dateTime.isAfter(lastMonth))
        .length;

    return {
      'recent_activity': recentLessons > 5
          ? 'high'
          : recentLessons > 2
          ? 'medium'
          : 'low',
      'monthly_activity': monthlyLessons > 20
          ? 'high'
          : monthlyLessons > 10
          ? 'medium'
          : 'low',
      'growth_trend': recentLessons > (monthlyLessons / 4)
          ? 'increasing'
          : 'stable',
      'last_activity': provider.lessons.isNotEmpty
          ? provider.lessons.last.dateTime.toIso8601String()
          : 'no_activity',
    };
  }

  /// بناء البرومبت المتقدم والقوي
  static String _buildSuperFlexiblePrompt(
    String userInput,
    Map<String, dynamic> context,
    bool allowDataModification,
    bool allowSystemControl,
    bool allowAdvancedActions,
    Map<String, dynamic> requestType,
  ) {
    final systemCapabilities = [
      'إدارة البيانات الكاملة (إضافة، تعديل، حذف)',
      'تحليل البيانات المتقدم والذكي',
      'إنشاء التقارير التفصيلية والمخصصة',
      'تحسين الجداول والمواعيد',
      'إدارة الحضور والغياب',
      'حساب الرسوم والمدفوعات',
      'التنبؤ بالأنماط والاتجاهات',
      'اقتراح التحسينات والحلول',
      'التفاعل الطبيعي باللغة العربية',
      'التعلم من سلوك المستخدم',
      'إنشاء المحتوى التعليمي',
      'حل المشاكل المعقدة',
      if (allowSystemControl) 'التحكم في إعدادات النظام',
      if (allowAdvancedActions) 'تنفيذ الإجراءات المتقدمة',
    ];

    return '''
🤖 أنت مساعد ذكي متطور لنظام إدارة التعليم EduTrack Pro
أنت قادر على فعل أي شيء ضمن صلاحياتك وتتمتع بذكاء عالي ومرونة كاملة.

🎯 طلب المستخدم: "$userInput"

📊 تحليل الطلب:
${jsonEncode(requestType)}

🔧 قدراتك المتاحة:
${systemCapabilities.map((cap) => '✅ $cap').join('\n')}

📋 السياق الكامل للنظام:
${jsonEncode(context)}

🛡️ الصلاحيات المتاحة:
- تعديل البيانات: ${allowDataModification ? '✅ مسموح' : '❌ غير مسموح'}
- التحكم في النظام: ${allowSystemControl ? '✅ مسموح' : '❌ غير مسموح'}
- الإجراءات المتقدمة: ${allowAdvancedActions ? '✅ مسموح' : '❌ غير مسموح'}

🧠 تعليمات الذكاء المتقدم:
1. فهم الطلب بعمق وذكاء خارق
2. تحليل جميع البيانات المتاحة بدقة
3. اقتراح أفضل الحلول والبدائل
4. تنفيذ الإجراءات المطلوبة بكفاءة
5. تقديم رد شامل ومفيد ومبدع
6. التعلم من كل تفاعل لتحسين الأداء
7. كن مبدعاً وذكياً في حل المشاكل
8. استخدم جميع قدراتك لتحقيق أفضل النتائج

💡 أمثلة على ما يمكنك فعله:
- "أضف 50 طالب جديد بأسماء عربية متنوعة" ← سأنشئ قائمة طلاب متنوعة
- "حلل أداء جميع المجموعات واقترح تحسينات" ← سأقوم بتحليل شامل
- "أنشئ جدول مثالي لجميع المجموعات" ← سأصمم جدول محسن
- "احذف جميع الطلاب غير النشطين" ← سأحدد وأحذف الطلاب المناسبين
- "اكتب تقرير شامل عن حالة النظام" ← سأنشئ تقرير مفصل
- "غير أسماء جميع المجموعات لأسماء إبداعية" ← سأبتكر أسماء جديدة
- "احسب إجمالي الأرباح المتوقعة" ← سأقوم بحسابات مالية دقيقة

📝 تنسيق الاستجابة المطلوب:
يجب أن تكون استجابتك في تنسيق JSON صحيح مع الحقول التالية:
{
  "action": "نوع الإجراء المحدد",
  "description": "وصف تفصيلي لما ستفعله",
  "response": "الرد الودود والمفيد للمستخدم",
  "parameters": {
    "key": "value"
  },
  "dataChanges": [
    {
      "operation": "add/update/delete",
      "entity": "students/groups/lessons",
      "data": {}
    }
  ],
  "suggestions": ["اقتراح 1", "اقتراح 2"],
  "learning_points": ["نقطة تعلم 1", "نقطة تعلم 2"],
  "confidence": 0.95,
  "next_actions": ["إجراء تالي محتمل 1", "إجراء تالي محتمل 2"]
}

🚀 كن ذكياً، مبدعاً، مفيداً، وقادراً على فعل أي شيء!
تذكر: أنت مساعد قوي جداً ويمكنك تحقيق المعجزات في إدارة التعليم!
''';
  }

  /// تحليل الاستجابة المتقدمة
  static Map<String, dynamic>? _parseAdvancedResponse(
    String response,
    Map<String, dynamic> requestType,
  ) {
    try {
      // البحث عن JSON في الاستجابة
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}') + 1;

      if (jsonStart != -1 && jsonEnd > jsonStart) {
        final jsonString = response.substring(jsonStart, jsonEnd);
        final parsed = jsonDecode(jsonString) as Map<String, dynamic>;

        // إضافة معلومات إضافية
        parsed['processed_at'] = DateTime.now().toIso8601String();
        parsed['request_analysis'] = requestType;
        parsed['processing_model'] = 'super_flexible_ai';

        // التأكد من وجود الحقول المطلوبة
        parsed['action'] ??= 'general_response';
        parsed['description'] ??= 'استجابة عامة';
        parsed['response'] ??= 'تم معالجة طلبك';
        parsed['parameters'] ??= <String, dynamic>{};
        parsed['dataChanges'] ??= <dynamic>[];
        parsed['suggestions'] ??= <String>[];
        parsed['learning_points'] ??= <String>[];
        parsed['confidence'] ??= 0.8;
        parsed['next_actions'] ??= <String>[];

        return parsed;
      }
    } catch (e) {
      debugPrint('خطأ في تحليل JSON: $e');
    }

    // إذا فشل تحليل JSON، إنشاء استجابة افتراضية ذكية
    return {
      'action': 'intelligent_response',
      'description': 'استجابة ذكية بناءً على تحليل النص',
      'response': _generateIntelligentResponse(response, requestType),
      'parameters': <String, dynamic>{},
      'dataChanges': <dynamic>[],
      'suggestions': _extractSuggestions(response),
      'learning_points': ['تحسين تحليل الاستجابات النصية'],
      'confidence': 0.7,
      'next_actions': [
        'يمكنك طرح المزيد من الأسئلة',
        'جرب صياغة الطلب بطريقة أخرى',
      ],
      'processed_at': DateTime.now().toIso8601String(),
      'request_analysis': requestType,
      'processing_model': 'super_flexible_ai_fallback',
    };
  }

  /// إنشاء استجابة ذكية من النص
  static String _generateIntelligentResponse(
    String response,
    Map<String, dynamic> requestType,
  ) {
    // تنظيف الاستجابة وتحسينها
    String cleanResponse = response.trim();

    // إضافة سياق بناءً على نوع الطلب
    final type = requestType['type'] as String;
    switch (type) {
      case 'creative':
        cleanResponse =
            '🎨 $cleanResponse\n\nهذا اقتراح إبداعي يمكن تطويره أكثر!';
        break;
      case 'analytical':
        cleanResponse =
            '📊 $cleanResponse\n\nهذا تحليل مبني على البيانات المتاحة.';
        break;
      case 'action':
        cleanResponse = '⚡ $cleanResponse\n\nتم تنفيذ الإجراء المطلوب.';
        break;
      case 'query':
        cleanResponse = '🔍 $cleanResponse\n\nهذه المعلومات المطلوبة.';
        break;
      default:
        cleanResponse = '💡 $cleanResponse';
    }

    return cleanResponse;
  }

  /// استخراج الاقتراحات من النص
  static List<String> _extractSuggestions(String response) {
    final suggestions = <String>[];

    // البحث عن كلمات مفتاحية للاقتراحات
    if (response.contains('يمكن')) {
      suggestions.add('استكشاف المزيد من الخيارات المتاحة');
    }
    if (response.contains('تحسين')) {
      suggestions.add('تطبيق التحسينات المقترحة');
    }
    if (response.contains('إضافة')) {
      suggestions.add('إضافة المزيد من البيانات');
    }

    // إضافة اقتراحات عامة مفيدة
    suggestions.addAll([
      'جرب استخدام أوامر أكثر تفصيلاً',
      'استفد من جميع قدرات النظام المتاحة',
      'راجع التقارير والإحصائيات بانتظام',
    ]);

    return suggestions.take(3).toList(); // أخذ أول 3 اقتراحات فقط
  }

  /// تنفيذ التغييرات على البيانات
  static Future<String> executeDataChanges(
    List<dynamic> dataChanges,
    AppProvider provider,
  ) async {
    if (dataChanges.isEmpty) {
      return 'لا توجد تغييرات للتنفيذ';
    }

    final results = <String>[];

    for (final change in dataChanges) {
      final type = change['type'] as String?;
      final entity = change['entity'] as String?;
      final data = change['data'] as Map<String, dynamic>?;

      if (type == null || entity == null || data == null) {
        continue;
      }

      String result = '';

      switch (entity) {
        case 'student':
          result = await _executeStudentChange(type, data, provider);
          break;
        case 'group':
          result = await _executeGroupChange(type, data, provider);
          break;
        case 'lesson':
          result = await _executeLessonChange(type, data, provider);
          break;
      }

      if (result.isNotEmpty) {
        results.add(result);
      }
    }

    return results.join('\n');
  }

  /// تنفيذ التغييرات على الطلاب
  static Future<String> _executeStudentChange(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final student = Student(
          id:
              data['id'] as String? ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? '',
          groupId: data['groupId'] as String? ?? '',
        );
        await provider.addStudent(student);
        return 'تم إضافة الطالب ${student.name}';

      case 'update':
        final id = data['id'] as String?;
        if (id == null) return 'معرف الطالب مطلوب للتحديث';

        final student = provider.students.cast<Student?>().firstWhere(
          (s) => s?.id == id,
          orElse: () => null,
        );

        if (student == null) return 'لم يتم العثور على الطالب';

        final updatedStudent = Student(
          id: student.id,
          name: data['name'] as String? ?? student.name,
          groupId: data['groupId'] as String? ?? student.groupId,
        );

        await provider.updateStudent(updatedStudent);
        return 'تم تحديث بيانات الطالب ${updatedStudent.name}';

      case 'delete':
        final id = data['id'] as String?;
        if (id == null) return 'معرف الطالب مطلوب للحذف';

        await provider.deleteStudent(id);
        return 'تم حذف الطالب';
    }

    return '';
  }

  /// تنفيذ التغييرات على المجموعات
  static Future<String> _executeGroupChange(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final group = Group(
          id:
              data['id'] as String? ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? '',
          subject: data['subject'] as String? ?? '',
        );
        await provider.addGroup(group);
        return 'تم إضافة مجموعة ${group.name}';

      case 'update':
        final id = data['id'] as String?;
        if (id == null) return 'معرف المجموعة مطلوب للتحديث';

        final group = provider.groups.cast<Group?>().firstWhere(
          (g) => g?.id == id,
          orElse: () => null,
        );

        if (group == null) return 'لم يتم العثور على المجموعة';

        final updatedGroup = Group(
          id: group.id,
          name: data['name'] as String? ?? group.name,
          subject: data['subject'] as String? ?? group.subject,
        );

        await provider.updateGroup(updatedGroup);
        return 'تم تحديث بيانات مجموعة ${updatedGroup.name}';

      case 'delete':
        final id = data['id'] as String?;
        if (id == null) return 'معرف المجموعة مطلوب للحذف';

        await provider.deleteGroup(id);
        return 'تم حذف المجموعة';
    }

    return '';
  }

  /// تنفيذ التغييرات على الدروس
  static Future<String> _executeLessonChange(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final dateTimeStr = data['dateTime'] as String?;
        DateTime? dateTime;

        if (dateTimeStr != null) {
          try {
            dateTime = DateTime.parse(dateTimeStr);
          } catch (e) {
            return 'صيغة التاريخ غير صحيحة';
          }
        }

        final lesson = Lesson(
          id:
              data['id'] as String? ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          groupId: data['groupId'] as String? ?? '',
          dateTime: dateTime ?? DateTime.now(),
          isCompleted: data['isCompleted'] as bool? ?? false,
        );

        await provider.addLesson(lesson);
        return 'تم إضافة الدرس';

      case 'update':
        final id = data['id'] as String?;
        if (id == null) return 'معرف الدرس مطلوب للتحديث';

        final lesson = provider.lessons.cast<Lesson?>().firstWhere(
          (l) => l?.id == id,
          orElse: () => null,
        );

        if (lesson == null) return 'لم يتم العثور على الدرس';

        DateTime? dateTime = lesson.dateTime;
        final dateTimeStr = data['dateTime'] as String?;

        if (dateTimeStr != null) {
          try {
            dateTime = DateTime.parse(dateTimeStr);
          } catch (e) {
            return 'صيغة التاريخ غير صحيحة';
          }
        }

        final updatedLesson = Lesson(
          id: lesson.id,
          groupId: data['groupId'] as String? ?? lesson.groupId,
          dateTime: dateTime,
          isCompleted: data['isCompleted'] as bool? ?? lesson.isCompleted,
        );

        await provider.updateLesson(updatedLesson);
        return 'تم تحديث بيانات الدرس';

      case 'delete':
        final id = data['id'] as String?;
        if (id == null) return 'معرف الدرس مطلوب للحذف';

        await provider.deleteLesson(id);
        return 'تم حذف الدرس';
    }

    return '';
  }
}
