import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/premium_card.dart';
import '../widgets/gradient_button.dart';
import '../models/group.dart';
import '../models/lesson.dart';

class ScheduleSettingsScreen extends StatefulWidget {
  const ScheduleSettingsScreen({super.key});

  @override
  State<ScheduleSettingsScreen> createState() => _ScheduleSettingsScreenState();
}

class _ScheduleSettingsScreenState extends State<ScheduleSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إعدادات الجدول',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppTheme.cardBg,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.darkBg,
              AppTheme.cardBg,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Consumer<AppProvider>(
            builder: (context, provider, child) {
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'جدولة الدروس الأسبوعية',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    if (provider.groups.isEmpty)
                      PremiumCard(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            'لا توجد مجموعات لجدولتها',
                            style: GoogleFonts.cairo(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      )
                    else
                      ...provider.groups.map((group) => _GroupScheduleCard(group: group)),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class _GroupScheduleCard extends StatefulWidget {
  final Group group;

  const _GroupScheduleCard({required this.group});

  @override
  State<_GroupScheduleCard> createState() => _GroupScheduleCardState();
}

class _GroupScheduleCardState extends State<_GroupScheduleCard> {
  final List<String> days = [
    'الأحد',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
  ];

  Map<String, List<String>> schedule = {};

  @override
  void initState() {
    super.initState();
    schedule = Map.from(widget.group.schedule);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.group.name,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          ...days.map((day) => _buildDaySchedule(day)),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: GradientButton(
                  text: 'حفظ الجدول',
                  onPressed: _saveSchedule,
                  height: 40,
                ),
              ),
              const SizedBox(width: 12),
              GradientButton(
                text: 'إنشاء دروس',
                onPressed: _generateLessons,
                height: 40,
                width: 100,
                colors: const [Color(0xFF10b981), Color(0xFF059669)],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDaySchedule(String day) {
    final dayTimes = schedule[day] ?? [];
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              day,
              style: GoogleFonts.cairo(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Wrap(
              spacing: 8,
              children: [
                ...dayTimes.map((time) => Chip(
                  label: Text(
                    time,
                    style: GoogleFonts.cairo(fontSize: 12),
                  ),
                  backgroundColor: const Color(0xFF6366f1).withValues(alpha: 0.2),
                  deleteIcon: const Icon(Icons.close, size: 16),
                  onDeleted: () {
                    setState(() {
                      dayTimes.remove(time);
                      if (dayTimes.isEmpty) {
                        schedule.remove(day);
                      }
                    });
                  },
                )),
                GestureDetector(
                  onTap: () => _addTimeSlot(day),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white30),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white70,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addTimeSlot(String day) async {
    final TimeOfDay? time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF6366f1),
              surface: Color(0xFF1e293b),
            ),
          ),
          child: child!,
        );
      },
    );

    if (time != null) {
      final timeString = '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
      setState(() {
        if (schedule[day] == null) {
          schedule[day] = [];
        }
        if (!schedule[day]!.contains(timeString)) {
          schedule[day]!.add(timeString);
          schedule[day]!.sort();
        }
      });
    }
  }

  void _saveSchedule() {
    widget.group.schedule = schedule;
    widget.group.save();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم حفظ الجدول بنجاح',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: const Color(0xFF10b981),
      ),
    );
  }

  void _generateLessons() async {
    final provider = Provider.of<AppProvider>(context, listen: false);
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday % 7));
    
    int lessonsCreated = 0;
    
    // Generate lessons for the next 4 weeks
    for (int week = 0; week < 4; week++) {
      for (String day in schedule.keys) {
        final dayIndex = days.indexOf(day);
        final lessonDate = startOfWeek.add(Duration(days: dayIndex + (week * 7)));
        
        for (String timeString in schedule[day]!) {
          final timeParts = timeString.split(':');
          final hour = int.parse(timeParts[0]);
          final minute = int.parse(timeParts[1]);
          
          final lessonDateTime = DateTime(
            lessonDate.year,
            lessonDate.month,
            lessonDate.day,
            hour,
            minute,
          );
          
          // Only create future lessons
          if (lessonDateTime.isAfter(now)) {
            final lesson = Lesson(
              id: '${widget.group.id}_${lessonDateTime.millisecondsSinceEpoch}',
              groupId: widget.group.id,
              dateTime: lessonDateTime,
            );
            
            await provider.addLesson(lesson);
            lessonsCreated++;
          }
        }
      }
    }
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم إنشاء $lessonsCreated درس',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: const Color(0xFF10b981),
        ),
      );
    }
  }
}