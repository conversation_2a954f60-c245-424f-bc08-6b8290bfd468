import '../models/student.dart';
import '../models/group.dart';
import '../models/lesson.dart';
import 'data_service.dart';

class SampleDataService {
  static Future<void> createSampleData() async {
    // Check if data already exists
    if (DataService.groups.isNotEmpty) return;

    // Create sample groups
    final mathGroup = Group(
      id: 'group_1',
      name: 'مجموعة الرياضيات المتقدمة',
      subject: 'الرياضيات',
      monthlyFee: 300.0,
      schedule: {
        'الأحد': ['10:00', '14:00'],
        'الثلاثاء': ['10:00'],
        'الخميس': ['14:00'],
      },
    );

    final scienceGroup = Group(
      id: 'group_2',
      name: 'مجموعة العلوم الطبيعية',
      subject: 'الفيزياء والكيمياء',
      monthlyFee: 250.0,
      schedule: {
        'الاثنين': ['16:00'],
        'الأربعاء': ['16:00'],
        'السبت': ['10:00'],
      },
    );

    await DataService.addGroup(mathGroup);
    await DataService.addGroup(scienceGroup);

    // Create sample students
    final students = [
      Student(
        id: 'student_1',
        name: 'أحمد محمد علي',
        groupId: 'group_1',
        monthlyPayment: 300.0,
        isPresent: true,
      ),
      Student(
        id: 'student_2',
        name: 'فاطمة أحمد حسن',
        groupId: 'group_1',
        monthlyPayment: 300.0,
        isPresent: false,
      ),
      Student(
        id: 'student_3',
        name: 'محمد عبدالله سالم',
        groupId: 'group_1',
        monthlyPayment: 300.0,
        isPresent: true,
      ),
      Student(
        id: 'student_4',
        name: 'نور الهدى محمود',
        groupId: 'group_2',
        monthlyPayment: 250.0,
        isPresent: true,
      ),
      Student(
        id: 'student_5',
        name: 'عبدالرحمن خالد',
        groupId: 'group_2',
        monthlyPayment: 250.0,
        isPresent: false,
      ),
    ];

    for (final student in students) {
      await DataService.addStudent(student);
    }

    // Create sample lessons for today and tomorrow
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    final lessons = [
      // Today's lessons
      Lesson(
        id: 'lesson_1',
        groupId: 'group_1',
        dateTime: today.add(const Duration(hours: 10)),
        isCompleted: true,
        attendedStudentIds: ['student_1', 'student_3'],
        notes: 'تم شرح الجبر الخطي',
      ),
      Lesson(
        id: 'lesson_2',
        groupId: 'group_1',
        dateTime: today.add(const Duration(hours: 14)),
        isCompleted: false,
        attendedStudentIds: [],
      ),
      Lesson(
        id: 'lesson_3',
        groupId: 'group_2',
        dateTime: today.add(const Duration(hours: 16)),
        isCompleted: false,
        attendedStudentIds: [],
      ),
      // Tomorrow's lessons
      Lesson(
        id: 'lesson_4',
        groupId: 'group_1',
        dateTime: tomorrow.add(const Duration(hours: 10)),
        isCompleted: false,
        attendedStudentIds: [],
      ),
      Lesson(
        id: 'lesson_5',
        groupId: 'group_2',
        dateTime: tomorrow.add(const Duration(hours: 16)),
        isCompleted: false,
        attendedStudentIds: [],
      ),
    ];

    for (final lesson in lessons) {
      await DataService.addLesson(lesson);
    }
  }
}