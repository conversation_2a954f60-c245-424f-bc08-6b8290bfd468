import 'package:hive/hive.dart';

part 'student.g.dart';

@HiveType(typeId: 0)
class Student extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String groupId;

  @HiveField(3)
  bool isPresent;

  @HiveField(4)
  double monthlyPayment;

  @HiveField(5)
  bool hasPaid;

  @HiveField(6)
  DateTime lastAttendance;

  Student({
    required this.id,
    required this.name,
    required this.groupId,
    this.isPresent = false,
    this.monthlyPayment = 0.0,
    this.hasPaid = false,
    DateTime? lastAttendance,
  }) : lastAttendance = lastAttendance ?? DateTime.now();
}