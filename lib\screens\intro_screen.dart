import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math' as math;
import '../theme/app_theme.dart';
import 'splash_screen.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  late AnimationController _backgroundController;
  
  final List<IntroPageData> _pages = [
    IntroPageData(
      title: 'مرحباً بك في EduTrack',
      description: 'تطبيق إدارة التعليم المتكامل لمساعدتك في تنظيم مجموعاتك وطلابك',
      icon: Icons.school_rounded,
      color: AppTheme.primaryBlue,
    ),
    IntroPageData(
      title: 'إدارة المجموعات',
      description: 'أنشئ وأدر مجموعات الطلاب بسهولة مع تتبع تقدمهم',
      icon: Icons.group,
      color: AppTheme.primaryPurple,
    ),
    IntroPageData(
      title: 'جدول الدروس',
      description: 'نظم جدولك الدراسي وتابع الدروس المكتملة والمتبقية',
      icon: Icons.schedule,
      color: AppTheme.accentPink,
    ),
    IntroPageData(
      title: 'لوحة التحكم',
      description: 'احصل على نظرة شاملة لأداء طلابك ومجموعاتك',
      icon: Icons.dashboard,
      color: AppTheme.success,
    ),
  ];

  @override
  void initState() {
    super.initState();
    
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    
    _backgroundController.repeat();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    } else {
      _startApp();
    }
  }

  void _startApp() async {
    // حفظ أن المستخدم قد رأى شاشة الترحيب
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('hasSeenIntro', true);
    
    // الانتقال إلى الشاشة التالية
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const SplashScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme.darkBg,
                AppTheme.darkBg.withValues(alpha: 0.9),
              ],
            ),
          ),
          child: Stack(
            children: [
              // Animated background
              AnimatedBuilder(
                animation: _backgroundController,
                builder: (context, child) {
                  return Positioned.fill(
                    child: CustomPaint(
                      painter: IntroBgPainter(_backgroundController.value),
                    ),
                  );
                },
              ),
              
              // Content
              SafeArea(
                child: Column(
                  children: [
                    Expanded(
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: _onPageChanged,
                        itemCount: _pages.length,
                        itemBuilder: (context, index) {
                          return _buildPage(_pages[index]);
                        },
                      ),
                    ),
                    
                    // Page indicator and buttons
                    Container(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Skip button
                          if (_currentPage < _pages.length - 1)
                            TextButton(
                              onPressed: _startApp,
                              child: Text(
                                'تخطي',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  color: Colors.white70,
                                ),
                              ),
                            )
                          else
                            const SizedBox(width: 60),
                            
                          // Page indicator
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: List.generate(
                              _pages.length,
                              (index) => Container(
                                margin: const EdgeInsets.symmetric(horizontal: 4),
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: _currentPage == index
                                      ? _pages[_currentPage].color
                                      : Colors.white.withValues(alpha: 0.3),
                                ),
                              ),
                            ),
                          ),
                          
                          // Next/Start button
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  _pages[_currentPage].color,
                                  _pages[_currentPage].color.withValues(alpha: 0.7),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(30),
                              boxShadow: [
                                BoxShadow(
                                  color: _pages[_currentPage].color.withValues(alpha: 0.4),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: IconButton(
                              onPressed: _nextPage,
                              icon: Icon(
                                _currentPage < _pages.length - 1
                                    ? Icons.arrow_forward
                                    : Icons.check,
                                color: Colors.white,
                              ),
                              iconSize: 24,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPage(IntroPageData page) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon with background
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  page.color,
                  page.color.withValues(alpha: 0.7),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: page.color.withValues(alpha: 0.5),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(
              page.icon,
              size: 60,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 40),
          
          // Title
          Text(
            page.title,
            style: GoogleFonts.cairo(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          
          // Description
          Text(
            page.description,
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.white70,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class IntroPageData {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  IntroPageData({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

class IntroBgPainter extends CustomPainter {
  final double animationValue;

  IntroBgPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    // Draw animated gradient shapes
    for (int i = 0; i < 8; i++) {
      final paint = Paint()
        ..shader = RadialGradient(
          colors: [
            _getColorForIndex(i).withValues(alpha: 0.3),
            _getColorForIndex(i).withValues(alpha: 0),
          ],
        ).createShader(
          Rect.fromCircle(
            center: _getPositionForIndex(i, size),
            radius: _getSizeForIndex(i),
          ),
        );

      canvas.drawCircle(
        _getPositionForIndex(i, size),
        _getSizeForIndex(i),
        paint,
      );
    }
  }

  Color _getColorForIndex(int index) {
    final colors = [
      AppTheme.primaryBlue,
      AppTheme.primaryPurple,
      AppTheme.accentPink,
      AppTheme.success,
      AppTheme.warning,
      AppTheme.info,
      AppTheme.primaryBlue,
      AppTheme.primaryPurple,
    ];
    return colors[index % colors.length];
  }

  Offset _getPositionForIndex(int index, Size size) {
    final baseX = size.width * (0.2 + (index % 4) * 0.2);
    final baseY = size.height * (0.2 + (index ~/ 4) * 0.2);
    
    // Add some animation to the position
    final offsetX = 100 * math.sin(animationValue * math.pi * 2 + index);
    final offsetY = 100 * math.cos(animationValue * math.pi * 2 + index * 0.7);
    
    return Offset(
      (baseX + offsetX).clamp(0, size.width),
      (baseY + offsetY).clamp(0, size.height),
    );
  }

  double _getSizeForIndex(int index) {
    // Base size with some variation
    final baseSize = 100 + index * 20;
    
    // Add pulsating effect
    final pulseFactor = 0.2 * math.sin(animationValue * math.pi * 2 + index * 0.5) + 1;
    
    return baseSize * pulseFactor;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}