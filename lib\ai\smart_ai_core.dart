import 'dart:convert';
import 'dart:io';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../providers/app_provider.dart';
import '../models/group.dart';
import '../models/student.dart';
import '../models/lesson.dart';
import '../services/data_service.dart';

/// نظام الذكاء الاصطناعي الذكي الجديد
class SmartAICore {
  static const String _apiKey =
      'AIzaSyBqJNYKJQKJQKJQKJQKJQKJQKJQKJQKJQK'; // ضع مفتاح API الصحيح هنا
  static GenerativeModel? _model;

  /// تهيئة النظام
  static void initialize() {
    try {
      _model = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );
      debugPrint('🤖 تم تهيئة نظام الذكاء الاصطناعي الذكي');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الذكاء الاصطناعي: $e');
    }
  }

  /// معالجة رسالة المستخدم بذكاء
  static Future<AIResponse> processMessage(
    String message,
    AppProvider provider,
  ) async {
    try {
      // تحليل نوع الرسالة
      final analysis = await _analyzeMessage(message);

      // بناء السياق
      final context = _buildContext(provider);

      // إنشاء البرومبت الذكي
      final prompt = _createSmartPrompt(message, analysis, context);

      // إرسال للذكاء الاصطناعي
      final response = await _sendToAI(prompt);

      // معالجة الاستجابة
      return _processResponse(response, analysis, provider);
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الرسالة: $e');
      return AIResponse.error('عذراً، حدث خطأ في معالجة طلبك');
    }
  }

  /// تحليل نوع الرسالة
  static Future<MessageAnalysis> _analyzeMessage(String message) async {
    try {
      final analysisPrompt =
          '''
حلل هذه الرسالة وحدد نوعها:
"$message"

أجب بـ JSON فقط:
{
  "type": "action|question|chat|greeting",
  "intent": "add|delete|update|view|help|casual",
  "entity": "student|group|lesson|attendance|none",
  "confidence": 0.95,
  "action_required": true
}
''';

      final response = await _sendToAI(analysisPrompt);
      final analysis = _parseJSON(response);

      return MessageAnalysis.fromMap(analysis);
    } catch (e) {
      // تحليل احتياطي بسيط
      return MessageAnalysis.fallback(message);
    }
  }

  /// بناء سياق البيانات
  static Map<String, dynamic> _buildContext(AppProvider provider) {
    return {
      'stats': {
        'total_students': provider.totalStudents,
        'total_groups': provider.totalGroups,
        'total_lessons': provider.lessons.length,
      },
      'recent_groups': provider.groups
          .take(3)
          .map(
            (g) => {
              'id': g.id,
              'name': g.name,
              'subject': g.subject,
              'student_count': g.studentIds.length,
            },
          )
          .toList(),
      'recent_students': provider.students
          .take(3)
          .map((s) => {'id': s.id, 'name': s.name, 'group_id': s.groupId})
          .toList(),
    };
  }

  /// إنشاء البرومبت الذكي مع شرح بنية JSON
  static String _createSmartPrompt(
    String message,
    MessageAnalysis analysis,
    Map<String, dynamic> context,
  ) {
    if (analysis.actionRequired) {
      return '''
أنت مساعد ذكي متطور لنظام إدارة التعليم EduTrack.

رسالة المستخدم: "$message"
نوع الطلب: ${analysis.type}
الهدف: ${analysis.intent}
الكيان: ${analysis.entity}

البيانات الحالية في النظام:
${jsonEncode(_sanitizeContext(context))}

## بنية ملف JSON الكاملة لنظام EduTrack:

### 1. بنية الطالب (Student):
{
  "id": "معرف فريد (timestamp)",
  "name": "اسم الطالب الكامل",
  "groupId": "معرف المجموعة التي ينتمي إليها",
  "isPresent": true/false,
  "monthlyPayment": رقم (الرسوم الشهرية),
  "hasPaid": true/false,
  "lastAttendance": "تاريخ آخر حضور بصيغة ISO"
}

### 2. بنية المجموعة (Group):
{
  "id": "معرف فريد (timestamp)",
  "name": "اسم المجموعة",
  "subject": "المادة الدراسية",
  "studentIds": ["قائمة معرفات الطلاب"]
}

### 3. بنية الدرس (Lesson):
{
  "id": "معرف فريد (timestamp)",
  "groupId": "معرف المجموعة",
  "dateTime": "تاريخ ووقت الدرس بصيغة ISO",
  "isCompleted": true/false,
  "attendedStudentIds": ["قائمة معرفات الطلاب الحاضرين"],
  "notes": "ملاحظات الدرس"
}

### 4. بنية ملف النسخة الاحتياطية الكامل:
{
  "students": [قائمة كائنات الطلاب],
  "groups": [قائمة كائنات المجموعات],
  "lessons": [قائمة كائنات الدروس],
  "timestamp": "تاريخ إنشاء النسخة الاحتياطية"
}

## تعليمات التنفيذ:
1. حلل طلب المستخدم بدقة
2. حدد نوع العملية المطلوبة (إضافة/تعديل/حذف/عرض)
3. أنشئ البيانات المطلوبة بالبنية الصحيحة
4. تأكد من صحة المعرفات والروابط بين الكائنات
5. استخدم timestamp حالي للمعرفات الجديدة

أجب بـ JSON فقط:
{
  "response": "رد مفيد ومختصر يشرح ما تم تنفيذه",
  "action": {
    "type": "add|update|delete|view|backup",
    "entity": "student|group|lesson|data",
    "data": {البيانات بالبنية الصحيحة حسب نوع الكيان},
    "json_operation": {
      "operation": "create|modify|delete|export",
      "target_file": "backup|live_data",
      "changes": "وصف التغييرات المطلوبة"
    }
  }
}
''';
    } else {
      return '''
أنت مساعد ودود لنظام إدارة التعليم EduTrack.

رسالة المستخدم: "$message"

أجب بشكل ودود ومفيد بـ JSON فقط:
{
  "response": "رد ودود ومفيد"
}
''';
    }
  }

  /// تنظيف السياق من DateTime objects
  static Map<String, dynamic> _sanitizeContext(Map<String, dynamic> context) {
    final sanitized = <String, dynamic>{};

    for (final entry in context.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is DateTime) {
        sanitized[key] = value.toIso8601String();
      } else if (value is List) {
        sanitized[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _sanitizeContext(item);
          } else if (item is DateTime) {
            return item.toIso8601String();
          }
          return item;
        }).toList();
      } else if (value is Map<String, dynamic>) {
        sanitized[key] = _sanitizeContext(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /// إرسال للذكاء الاصطناعي
  static Future<String> _sendToAI(String prompt) async {
    if (_model == null) {
      throw Exception('النموذج غير مهيأ');
    }

    final content = [Content.text(prompt)];
    final response = await _model!.generateContent(content);

    return response.text ?? 'لا توجد استجابة';
  }

  /// معالجة الاستجابة
  static Future<AIResponse> _processResponse(
    String response,
    MessageAnalysis analysis,
    AppProvider provider,
  ) async {
    try {
      final data = _parseJSON(response);
      final responseText = data['response'] as String? ?? 'تم معالجة طلبك';

      // تنفيذ الإجراء إذا كان مطلوباً
      if (data.containsKey('action')) {
        final actionResult = await _executeAction(data['action'], provider);

        // تنفيذ عمليات JSON إذا كانت مطلوبة
        String jsonResult = '';
        if (data['action'].containsKey('json_operation')) {
          jsonResult = await _executeJSONOperation(
            data['action']['json_operation'],
            data['action'],
            provider,
          );
        }

        final fullResult = [
          responseText,
          actionResult,
          jsonResult,
        ].where((s) => s.isNotEmpty).join('\n');

        return AIResponse.success(fullResult, hasAction: true);
      }

      return AIResponse.success(responseText);
    } catch (e) {
      return AIResponse.error('عذراً، لم أتمكن من فهم الطلب');
    }
  }

  /// تنفيذ عمليات JSON المباشرة
  static Future<String> _executeJSONOperation(
    Map<String, dynamic> jsonOp,
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      final operation = jsonOp['operation'] as String?;
      final targetFile = jsonOp['target_file'] as String?;
      final changes = jsonOp['changes'] as String?;

      switch (operation) {
        case 'create':
          return await _createJSONBackup(action, provider);
        case 'modify':
          return await _modifyJSONData(action, provider);
        case 'export':
          return await _exportJSONData(action, provider);
        default:
          return '📄 عملية JSON: $changes';
      }
    } catch (e) {
      return '❌ خطأ في عملية JSON: $e';
    }
  }

  /// إنشاء نسخة احتياطية JSON
  static Future<String> _createJSONBackup(
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      // جمع البيانات الحالية
      final backupData = {
        'students': provider.students
            .map(
              (s) => {
                'id': s.id,
                'name': s.name,
                'groupId': s.groupId,
                'isPresent': s.isPresent,
                'monthlyPayment': s.monthlyPayment,
                'hasPaid': s.hasPaid,
                'lastAttendance': s.lastAttendance.toIso8601String(),
              },
            )
            .toList(),
        'groups': provider.groups
            .map(
              (g) => {
                'id': g.id,
                'name': g.name,
                'subject': g.subject,
                'studentIds': g.studentIds,
              },
            )
            .toList(),
        'lessons': provider.lessons
            .map(
              (l) => {
                'id': l.id,
                'groupId': l.groupId,
                'dateTime': l.dateTime.toIso8601String(),
                'isCompleted': l.isCompleted,
                'attendedStudentIds': l.attendedStudentIds,
                'notes': l.notes,
              },
            )
            .toList(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      // تحويل إلى JSON منسق
      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);

      return '''
📄 تم إنشاء ملف JSON:

```json
${jsonString.length > 500 ? '${jsonString.substring(0, 500)}...' : jsonString}
```

📊 إحصائيات الملف:
• الطلاب: ${(backupData['students'] as List?)?.length ?? 0}
• المجموعات: ${(backupData['groups'] as List?)?.length ?? 0}
• الدروس: ${(backupData['lessons'] as List?)?.length ?? 0}
• حجم الملف: ${(jsonString.length / 1024).toStringAsFixed(1)} KB
''';
    } catch (e) {
      return '❌ فشل في إنشاء ملف JSON: $e';
    }
  }

  /// تعديل بيانات JSON
  static Future<String> _modifyJSONData(
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      final entity = action['entity'] as String?;
      final data = action['data'] as Map<String, dynamic>?;

      if (entity == null || data == null) {
        return '❌ بيانات غير مكتملة للتعديل';
      }

      // إنشاء JSON للكائن الجديد/المعدل
      Map<String, dynamic> jsonObject = {};

      switch (entity) {
        case 'student':
          jsonObject = {
            'id':
                data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
            'name': data['name'] ?? 'طالب جديد',
            'groupId': data['groupId'] ?? '',
            'isPresent': data['isPresent'] ?? false,
            'monthlyPayment': data['monthlyPayment'] ?? 0.0,
            'hasPaid': data['hasPaid'] ?? false,
            'lastAttendance': DateTime.now().toIso8601String(),
          };
          break;
        case 'group':
          jsonObject = {
            'id':
                data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
            'name': data['name'] ?? 'مجموعة جديدة',
            'subject': data['subject'] ?? 'عام',
            'studentIds': data['studentIds'] ?? [],
          };
          break;
        case 'lesson':
          jsonObject = {
            'id':
                data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
            'groupId': data['groupId'] ?? '',
            'dateTime': data['dateTime'] ?? DateTime.now().toIso8601String(),
            'isCompleted': data['isCompleted'] ?? false,
            'attendedStudentIds': data['attendedStudentIds'] ?? [],
            'notes': data['notes'] ?? '',
          };
          break;
      }

      final jsonString = const JsonEncoder.withIndent('  ').convert(jsonObject);

      return '''
📝 تم تعديل بيانات JSON للـ$entity:

```json
$jsonString
```

✅ البيانات جاهزة للحفظ في النظام
''';
    } catch (e) {
      return '❌ فشل في تعديل بيانات JSON: $e';
    }
  }

  /// تصدير بيانات JSON
  static Future<String> _exportJSONData(
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      final entity = action['entity'] as String?;

      Map<String, dynamic> exportData = {};

      switch (entity) {
        case 'students':
          exportData = {
            'students': provider.students
                .map(
                  (s) => {
                    'id': s.id,
                    'name': s.name,
                    'groupId': s.groupId,
                    'isPresent': s.isPresent,
                    'monthlyPayment': s.monthlyPayment,
                    'hasPaid': s.hasPaid,
                    'lastAttendance': s.lastAttendance.toIso8601String(),
                  },
                )
                .toList(),
            'export_date': DateTime.now().toIso8601String(),
          };
          break;
        case 'groups':
          exportData = {
            'groups': provider.groups
                .map(
                  (g) => {
                    'id': g.id,
                    'name': g.name,
                    'subject': g.subject,
                    'studentIds': g.studentIds,
                  },
                )
                .toList(),
            'export_date': DateTime.now().toIso8601String(),
          };
          break;
        default:
          exportData = {
            'message': 'نوع البيانات غير مدعوم للتصدير',
            'supported_types': ['students', 'groups', 'lessons'],
          };
      }

      final jsonString = const JsonEncoder.withIndent('  ').convert(exportData);

      return '''
📤 تم تصدير بيانات $entity:

```json
${jsonString.length > 800 ? '${jsonString.substring(0, 800)}...' : jsonString}
```

💾 الملف جاهز للحفظ أو المشاركة
''';
    } catch (e) {
      return '❌ فشل في تصدير البيانات: $e';
    }
  }

  /// حفظ ملف JSON على الجهاز
  static Future<String> saveJSONToFile(
    Map<String, dynamic> data,
    String fileName,
  ) async {
    try {
      // الحصول على مجلد التحميلات
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.json');

      // تحويل البيانات إلى JSON منسق
      final jsonString = const JsonEncoder.withIndent('  ').convert(data);

      // كتابة الملف
      await file.writeAsString(jsonString);

      return '''
💾 تم حفظ ملف JSON بنجاح:

📁 المسار: ${file.path}
📊 الحجم: ${(jsonString.length / 1024).toStringAsFixed(1)} KB
📅 التاريخ: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}

✅ يمكنك الآن مشاركة الملف أو استخدامه كنسخة احتياطية
''';
    } catch (e) {
      return '❌ فشل في حفظ الملف: $e';
    }
  }

  /// قراءة ملف JSON من الجهاز
  static Future<String> loadJSONFromFile(String filePath) async {
    try {
      final file = File(filePath);

      if (!await file.exists()) {
        return '❌ الملف غير موجود: $filePath';
      }

      final jsonString = await file.readAsString();
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      return '''
📂 تم تحميل ملف JSON بنجاح:

📁 المسار: $filePath
📊 الحجم: ${(jsonString.length / 1024).toStringAsFixed(1)} KB

📋 محتويات الملف:
• الطلاب: ${(data['students'] as List?)?.length ?? 0}
• المجموعات: ${(data['groups'] as List?)?.length ?? 0}
• الدروس: ${(data['lessons'] as List?)?.length ?? 0}

✅ البيانات جاهزة للاستيراد
''';
    } catch (e) {
      return '❌ فشل في قراءة الملف: $e';
    }
  }

  /// تحليل وإصلاح ملف JSON
  static Future<String> analyzeAndFixJSON(String jsonString) async {
    try {
      // محاولة تحليل JSON
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      // فحص البنية
      final issues = <String>[];
      final fixes = <String>[];

      // فحص الطلاب
      if (data.containsKey('students')) {
        final students = data['students'] as List?;
        if (students != null) {
          for (int i = 0; i < students.length; i++) {
            final student = students[i] as Map<String, dynamic>?;
            if (student != null) {
              // فحص الحقول المطلوبة
              if (!student.containsKey('id')) {
                student['id'] = DateTime.now().millisecondsSinceEpoch
                    .toString();
                fixes.add('تم إضافة معرف للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('name')) {
                student['name'] = 'طالب ${i + 1}';
                fixes.add('تم إضافة اسم للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('groupId')) {
                student['groupId'] = '';
                fixes.add('تم إضافة معرف مجموعة للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('isPresent')) {
                student['isPresent'] = false;
                fixes.add('تم إضافة حالة حضور للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('monthlyPayment')) {
                student['monthlyPayment'] = 0.0;
                fixes.add('تم إضافة رسوم شهرية للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('hasPaid')) {
                student['hasPaid'] = false;
                fixes.add('تم إضافة حالة دفع للطالب رقم ${i + 1}');
              }
              if (!student.containsKey('lastAttendance')) {
                student['lastAttendance'] = DateTime.now().toIso8601String();
                fixes.add('تم إضافة تاريخ آخر حضور للطالب رقم ${i + 1}');
              }
            }
          }
        }
      }

      // فحص المجموعات
      if (data.containsKey('groups')) {
        final groups = data['groups'] as List?;
        if (groups != null) {
          for (int i = 0; i < groups.length; i++) {
            final group = groups[i] as Map<String, dynamic>?;
            if (group != null) {
              if (!group.containsKey('id')) {
                group['id'] = DateTime.now().millisecondsSinceEpoch.toString();
                fixes.add('تم إضافة معرف للمجموعة رقم ${i + 1}');
              }
              if (!group.containsKey('name')) {
                group['name'] = 'مجموعة ${i + 1}';
                fixes.add('تم إضافة اسم للمجموعة رقم ${i + 1}');
              }
              if (!group.containsKey('subject')) {
                group['subject'] = 'عام';
                fixes.add('تم إضافة مادة للمجموعة رقم ${i + 1}');
              }
              if (!group.containsKey('studentIds')) {
                group['studentIds'] = <String>[];
                fixes.add('تم إضافة قائمة طلاب للمجموعة رقم ${i + 1}');
              }
            }
          }
        }
      }

      // إنشاء JSON محسن
      final fixedJsonString = const JsonEncoder.withIndent('  ').convert(data);

      return '''
🔍 تحليل ملف JSON:

${issues.isEmpty ? '✅ لا توجد مشاكل في البنية' : '⚠️ تم العثور على ${issues.length} مشكلة'}

${fixes.isEmpty ? '' : '''
🔧 الإصلاحات المطبقة:
${fixes.map((fix) => '• $fix').join('\n')}
'''}

📄 JSON المحسن:
```json
${fixedJsonString.length > 1000 ? '${fixedJsonString.substring(0, 1000)}...' : fixedJsonString}
```

✅ الملف جاهز للاستخدام
''';
    } catch (e) {
      return '''
❌ خطأ في تحليل JSON:

🔍 الخطأ: $e

💡 اقتراحات للإصلاح:
• تأكد من صحة تنسيق JSON
• تحقق من وجود جميع الأقواس والفواصل
• تأكد من أن النصوص محاطة بعلامات اقتباس
• تحقق من عدم وجود فواصل إضافية

📝 مثال على البنية الصحيحة:
```json
{
  "students": [
    {
      "id": "123456789",
      "name": "اسم الطالب",
      "groupId": "معرف المجموعة"
    }
  ]
}
```
''';
    }
  }

  /// تنفيذ الإجراءات
  static Future<String> _executeAction(
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      final type = action['type'] as String?;
      final entity = action['entity'] as String?;
      final data = action['data'] as Map<String, dynamic>?;

      if (type == null || entity == null || data == null) {
        return 'بيانات الإجراء غير مكتملة';
      }

      switch (entity) {
        case 'group':
          return await _handleGroupAction(type, data, provider);
        case 'student':
          return await _handleStudentAction(type, data, provider);
        case 'lesson':
          return await _handleLessonAction(type, data, provider);
        default:
          return 'نوع الكيان غير مدعوم';
      }
    } catch (e) {
      return 'فشل في تنفيذ الإجراء: $e';
    }
  }

  /// معالجة إجراءات المجموعات
  static Future<String> _handleGroupAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final group = Group(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? 'مجموعة جديدة',
          subject: data['subject'] as String? ?? 'عام',
          studentIds: [],
        );
        await provider.addGroup(group);
        return '✅ تم إضافة مجموعة "${group.name}" بنجاح';

      case 'delete':
        final groupId = data['id'] as String?;
        if (groupId != null) {
          await provider.deleteGroup(groupId);
          return '✅ تم حذف المجموعة بنجاح';
        }
        return '❌ معرف المجموعة مطلوب للحذف';

      default:
        return '❌ إجراء غير مدعوم للمجموعات';
    }
  }

  /// معالجة إجراءات الطلاب
  static Future<String> _handleStudentAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final student = Student(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? 'طالب جديد',
          groupId: data['groupId'] as String? ?? '',
        );
        await provider.addStudent(student);
        return '✅ تم إضافة طالب "${student.name}" بنجاح';

      case 'delete':
        final studentId = data['id'] as String?;
        if (studentId != null) {
          await provider.deleteStudent(studentId);
          return '✅ تم حذف الطالب بنجاح';
        }
        return '❌ معرف الطالب مطلوب للحذف';

      default:
        return '❌ إجراء غير مدعوم للطلاب';
    }
  }

  /// معالجة إجراءات الدروس
  static Future<String> _handleLessonAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final lesson = Lesson(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          groupId: data['groupId'] as String? ?? '',
          dateTime: DateTime.now(),
          isCompleted: false,
        );
        await provider.addLesson(lesson);
        return '✅ تم إضافة درس جديد بنجاح';

      default:
        return '❌ إجراء غير مدعوم للدروس';
    }
  }

  /// تحليل JSON مع معالجة الأخطاء
  static Map<String, dynamic> _parseJSON(String text) {
    try {
      // تنظيف النص من markdown
      String cleanText = text.trim();
      if (cleanText.startsWith('```json')) {
        cleanText = cleanText.substring(7);
      }
      if (cleanText.startsWith('```')) {
        cleanText = cleanText.substring(3);
      }
      if (cleanText.endsWith('```')) {
        cleanText = cleanText.substring(0, cleanText.length - 3);
      }

      // البحث عن JSON
      final jsonStart = cleanText.indexOf('{');
      final jsonEnd = cleanText.lastIndexOf('}') + 1;

      if (jsonStart != -1 && jsonEnd > jsonStart) {
        final jsonString = cleanText.substring(jsonStart, jsonEnd);
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }

      // إذا لم يوجد JSON، إنشاء استجابة افتراضية
      return {'response': cleanText};
    } catch (e) {
      return {'response': text};
    }
  }
}

/// تحليل الرسالة
class MessageAnalysis {
  final String type;
  final String intent;
  final String entity;
  final double confidence;
  final bool actionRequired;

  MessageAnalysis({
    required this.type,
    required this.intent,
    required this.entity,
    required this.confidence,
    required this.actionRequired,
  });

  factory MessageAnalysis.fromMap(Map<String, dynamic> map) {
    return MessageAnalysis(
      type: map['type'] as String? ?? 'chat',
      intent: map['intent'] as String? ?? 'casual',
      entity: map['entity'] as String? ?? 'none',
      confidence: (map['confidence'] as num?)?.toDouble() ?? 0.5,
      actionRequired: map['action_required'] as bool? ?? false,
    );
  }

  factory MessageAnalysis.fallback(String message) {
    final lowerMessage = message.toLowerCase();

    // تحليل بسيط للكلمات المفتاحية
    bool hasAction =
        lowerMessage.contains('أضف') ||
        lowerMessage.contains('احذف') ||
        lowerMessage.contains('أنشئ');

    String entity = 'none';
    if (lowerMessage.contains('مجموعة')) entity = 'group';
    if (lowerMessage.contains('طالب')) entity = 'student';
    if (lowerMessage.contains('درس')) entity = 'lesson';

    return MessageAnalysis(
      type: hasAction ? 'action' : 'chat',
      intent: hasAction ? 'add' : 'casual',
      entity: entity,
      confidence: 0.7,
      actionRequired: hasAction,
    );
  }
}

/// استجابة الذكاء الاصطناعي
class AIResponse {
  final String message;
  final bool isSuccess;
  final bool hasAction;
  final String? error;

  AIResponse({
    required this.message,
    required this.isSuccess,
    this.hasAction = false,
    this.error,
  });

  factory AIResponse.success(String message, {bool hasAction = false}) {
    return AIResponse(message: message, isSuccess: true, hasAction: hasAction);
  }

  factory AIResponse.error(String error) {
    return AIResponse(message: error, isSuccess: false, error: error);
  }
}
