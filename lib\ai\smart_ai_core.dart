import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../providers/app_provider.dart';
import '../models/group.dart';
import '../models/student.dart';
import '../models/lesson.dart';

/// نظام الذكاء الاصطناعي الذكي الجديد
class SmartAICore {
  static const String _apiKey =
      'AIzaSyBqJNYKJQKJQKJQKJQKJQKJQKJQKJQKJQK'; // ضع مفتاح API الصحيح هنا
  static GenerativeModel? _model;

  /// تهيئة النظام
  static void initialize() {
    try {
      _model = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: _apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );
      debugPrint('🤖 تم تهيئة نظام الذكاء الاصطناعي الذكي');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الذكاء الاصطناعي: $e');
    }
  }

  /// معالجة رسالة المستخدم بذكاء
  static Future<AIResponse> processMessage(
    String message,
    AppProvider provider,
  ) async {
    try {
      // تحليل نوع الرسالة
      final analysis = await _analyzeMessage(message);

      // بناء السياق
      final context = _buildContext(provider);

      // إنشاء البرومبت الذكي
      final prompt = _createSmartPrompt(message, analysis, context);

      // إرسال للذكاء الاصطناعي
      final response = await _sendToAI(prompt);

      // معالجة الاستجابة
      return _processResponse(response, analysis, provider);
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الرسالة: $e');
      return AIResponse.error('عذراً، حدث خطأ في معالجة طلبك');
    }
  }

  /// تحليل نوع الرسالة
  static Future<MessageAnalysis> _analyzeMessage(String message) async {
    try {
      final analysisPrompt =
          '''
حلل هذه الرسالة وحدد نوعها:
"$message"

أجب بـ JSON فقط:
{
  "type": "action|question|chat|greeting",
  "intent": "add|delete|update|view|help|casual",
  "entity": "student|group|lesson|attendance|none",
  "confidence": 0.95,
  "action_required": true
}
''';

      final response = await _sendToAI(analysisPrompt);
      final analysis = _parseJSON(response);

      return MessageAnalysis.fromMap(analysis);
    } catch (e) {
      // تحليل احتياطي بسيط
      return MessageAnalysis.fallback(message);
    }
  }

  /// بناء سياق البيانات
  static Map<String, dynamic> _buildContext(AppProvider provider) {
    return {
      'stats': {
        'total_students': provider.totalStudents,
        'total_groups': provider.totalGroups,
        'total_lessons': provider.lessons.length,
      },
      'recent_groups': provider.groups
          .take(3)
          .map(
            (g) => {
              'id': g.id,
              'name': g.name,
              'subject': g.subject,
              'student_count': g.studentIds.length,
            },
          )
          .toList(),
      'recent_students': provider.students
          .take(3)
          .map((s) => {'id': s.id, 'name': s.name, 'group_id': s.groupId})
          .toList(),
    };
  }

  /// إنشاء البرومبت الذكي
  static String _createSmartPrompt(
    String message,
    MessageAnalysis analysis,
    Map<String, dynamic> context,
  ) {
    if (analysis.actionRequired) {
      return '''
أنت مساعد ذكي لنظام إدارة التعليم EduTrack.

رسالة المستخدم: "$message"
نوع الطلب: ${analysis.type}
الهدف: ${analysis.intent}
الكيان: ${analysis.entity}

البيانات الحالية:
${jsonEncode(context)}

إذا كان الطلب يتطلب تنفيذ إجراء، أجب بـ JSON:
{
  "response": "رد مفيد ومختصر",
  "action": {
    "type": "add|update|delete|view",
    "entity": "student|group|lesson",
    "data": {"name": "الاسم", "subject": "المادة"}
  }
}

إذا كان سؤال عادي، أجب بـ JSON:
{
  "response": "إجابة مفيدة ومختصرة"
}
''';
    } else {
      return '''
أنت مساعد ودود لنظام إدارة التعليم.

رسالة المستخدم: "$message"

أجب بشكل ودود ومفيد بـ JSON:
{
  "response": "رد ودود ومفيد"
}
''';
    }
  }

  /// إرسال للذكاء الاصطناعي
  static Future<String> _sendToAI(String prompt) async {
    if (_model == null) {
      throw Exception('النموذج غير مهيأ');
    }

    final content = [Content.text(prompt)];
    final response = await _model!.generateContent(content);

    return response.text ?? 'لا توجد استجابة';
  }

  /// معالجة الاستجابة
  static Future<AIResponse> _processResponse(
    String response,
    MessageAnalysis analysis,
    AppProvider provider,
  ) async {
    try {
      final data = _parseJSON(response);
      final responseText = data['response'] as String? ?? 'تم معالجة طلبك';

      // تنفيذ الإجراء إذا كان مطلوباً
      if (data.containsKey('action')) {
        final actionResult = await _executeAction(data['action'], provider);
        return AIResponse.success(
          '$responseText\n$actionResult',
          hasAction: true,
        );
      }

      return AIResponse.success(responseText);
    } catch (e) {
      return AIResponse.error('عذراً، لم أتمكن من فهم الطلب');
    }
  }

  /// تنفيذ الإجراءات
  static Future<String> _executeAction(
    Map<String, dynamic> action,
    AppProvider provider,
  ) async {
    try {
      final type = action['type'] as String?;
      final entity = action['entity'] as String?;
      final data = action['data'] as Map<String, dynamic>?;

      if (type == null || entity == null || data == null) {
        return 'بيانات الإجراء غير مكتملة';
      }

      switch (entity) {
        case 'group':
          return await _handleGroupAction(type, data, provider);
        case 'student':
          return await _handleStudentAction(type, data, provider);
        case 'lesson':
          return await _handleLessonAction(type, data, provider);
        default:
          return 'نوع الكيان غير مدعوم';
      }
    } catch (e) {
      return 'فشل في تنفيذ الإجراء: $e';
    }
  }

  /// معالجة إجراءات المجموعات
  static Future<String> _handleGroupAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final group = Group(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? 'مجموعة جديدة',
          subject: data['subject'] as String? ?? 'عام',
          studentIds: [],
        );
        await provider.addGroup(group);
        return '✅ تم إضافة مجموعة "${group.name}" بنجاح';

      case 'delete':
        final groupId = data['id'] as String?;
        if (groupId != null) {
          await provider.deleteGroup(groupId);
          return '✅ تم حذف المجموعة بنجاح';
        }
        return '❌ معرف المجموعة مطلوب للحذف';

      default:
        return '❌ إجراء غير مدعوم للمجموعات';
    }
  }

  /// معالجة إجراءات الطلاب
  static Future<String> _handleStudentAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final student = Student(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: data['name'] as String? ?? 'طالب جديد',
          groupId: data['groupId'] as String? ?? '',
        );
        await provider.addStudent(student);
        return '✅ تم إضافة طالب "${student.name}" بنجاح';

      case 'delete':
        final studentId = data['id'] as String?;
        if (studentId != null) {
          await provider.deleteStudent(studentId);
          return '✅ تم حذف الطالب بنجاح';
        }
        return '❌ معرف الطالب مطلوب للحذف';

      default:
        return '❌ إجراء غير مدعوم للطلاب';
    }
  }

  /// معالجة إجراءات الدروس
  static Future<String> _handleLessonAction(
    String type,
    Map<String, dynamic> data,
    AppProvider provider,
  ) async {
    switch (type) {
      case 'add':
        final lesson = Lesson(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          groupId: data['groupId'] as String? ?? '',
          dateTime: DateTime.now(),
          isCompleted: false,
        );
        await provider.addLesson(lesson);
        return '✅ تم إضافة درس جديد بنجاح';

      default:
        return '❌ إجراء غير مدعوم للدروس';
    }
  }

  /// تحليل JSON مع معالجة الأخطاء
  static Map<String, dynamic> _parseJSON(String text) {
    try {
      // تنظيف النص من markdown
      String cleanText = text.trim();
      if (cleanText.startsWith('```json')) {
        cleanText = cleanText.substring(7);
      }
      if (cleanText.startsWith('```')) {
        cleanText = cleanText.substring(3);
      }
      if (cleanText.endsWith('```')) {
        cleanText = cleanText.substring(0, cleanText.length - 3);
      }

      // البحث عن JSON
      final jsonStart = cleanText.indexOf('{');
      final jsonEnd = cleanText.lastIndexOf('}') + 1;

      if (jsonStart != -1 && jsonEnd > jsonStart) {
        final jsonString = cleanText.substring(jsonStart, jsonEnd);
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }

      // إذا لم يوجد JSON، إنشاء استجابة افتراضية
      return {'response': cleanText};
    } catch (e) {
      return {'response': text};
    }
  }
}

/// تحليل الرسالة
class MessageAnalysis {
  final String type;
  final String intent;
  final String entity;
  final double confidence;
  final bool actionRequired;

  MessageAnalysis({
    required this.type,
    required this.intent,
    required this.entity,
    required this.confidence,
    required this.actionRequired,
  });

  factory MessageAnalysis.fromMap(Map<String, dynamic> map) {
    return MessageAnalysis(
      type: map['type'] as String? ?? 'chat',
      intent: map['intent'] as String? ?? 'casual',
      entity: map['entity'] as String? ?? 'none',
      confidence: (map['confidence'] as num?)?.toDouble() ?? 0.5,
      actionRequired: map['action_required'] as bool? ?? false,
    );
  }

  factory MessageAnalysis.fallback(String message) {
    final lowerMessage = message.toLowerCase();

    // تحليل بسيط للكلمات المفتاحية
    bool hasAction =
        lowerMessage.contains('أضف') ||
        lowerMessage.contains('احذف') ||
        lowerMessage.contains('أنشئ');

    String entity = 'none';
    if (lowerMessage.contains('مجموعة')) entity = 'group';
    if (lowerMessage.contains('طالب')) entity = 'student';
    if (lowerMessage.contains('درس')) entity = 'lesson';

    return MessageAnalysis(
      type: hasAction ? 'action' : 'chat',
      intent: hasAction ? 'add' : 'casual',
      entity: entity,
      confidence: 0.7,
      actionRequired: hasAction,
    );
  }
}

/// استجابة الذكاء الاصطناعي
class AIResponse {
  final String message;
  final bool isSuccess;
  final bool hasAction;
  final String? error;

  AIResponse({
    required this.message,
    required this.isSuccess,
    this.hasAction = false,
    this.error,
  });

  factory AIResponse.success(String message, {bool hasAction = false}) {
    return AIResponse(message: message, isSuccess: true, hasAction: hasAction);
  }

  factory AIResponse.error(String error) {
    return AIResponse(message: error, isSuccess: false, error: error);
  }
}
