import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../widgets/enhanced_card.dart';
import '../widgets/flexible_ai_assistant.dart';
import '../widgets/stats_dashboard.dart';
import '../theme/app_theme.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Header
              EnhancedCard(
                type: CardType.accent,
                enableHoverEffect: true,
                enablePressEffect: false,
                padding: const EdgeInsets.all(28),
                customGradient: AppTheme.primaryGradient,
                child: Row(
                  children: [
                    // Enhanced Logo Container
                    Container(
                      padding: const EdgeInsets.all(14),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white.withValues(alpha: 0.25),
                            Colors.white.withValues(alpha: 0.15),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.asset(
                          'lib/logo/icon.png',
                          width: 32,
                          height: 32,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                gradient: AppTheme.primaryGradientReverse,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.dashboard_rounded,
                                color: Colors.white,
                                size: 20,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Enhanced Welcome Text
                          ShaderMask(
                            shaderCallback: (bounds) => LinearGradient(
                              colors: [
                                Colors.white.withValues(alpha: 0.9),
                                Colors.white.withValues(alpha: 0.7),
                              ],
                            ).createShader(bounds),
                            child: Text(
                              'مرحباً بك في',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(height: 4),
                          // Enhanced Title with Gradient
                          ShaderMask(
                            shaderCallback: (bounds) => const LinearGradient(
                              colors: [Colors.white, Color(0xFFF0F9FF)],
                            ).createShader(bounds),
                            child: Text(
                              'EduTrack',
                              style: GoogleFonts.cairo(
                                fontSize: 26,
                                fontWeight: FontWeight.w800,
                                color: Colors.white,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                          const SizedBox(height: 2),
                          // Subtitle
                          Text(
                            'نظام إدارة التعليم المتقدم',
                            style: GoogleFonts.cairo(
                              fontSize: 13,
                              color: Colors.white.withValues(alpha: 0.8),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              // AI Assistant
              const FlexibleAIAssistant(),
              const SizedBox(height: 24),
              // Enhanced Stats Dashboard
              QuickStats(
                totalGroups: provider.totalGroups,
                totalStudents: provider.totalStudents,
                completedLessons: provider.completedLessonsToday,
                remainingLessons: provider.remainingLessonsToday,
                onGroupsTap: () => _showGroupsModal(context, provider),
                onStudentsTap: () => _showStudentsModal(context, provider),
                onCompletedTap: () =>
                    _showCompletedLessonsModal(context, provider),
                onRemainingTap: () =>
                    _showRemainingLessonsModal(context, provider),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showGroupsModal(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBg,
        title: Text(
          'المجموعات',
          style: GoogleFonts.cairo(color: AppTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: provider.groups.length,
            itemBuilder: (context, index) {
              final group = provider.groups[index];
              return ListTile(
                title: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: AppTheme.textPrimary),
                ),
                subtitle: Text(
                  group.subject,
                  style: GoogleFonts.cairo(color: AppTheme.textSecondary),
                ),
                leading: Icon(Icons.groups_rounded, color: AppTheme.primary),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: AppTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showStudentsModal(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBg,
        title: Text(
          'الطلاب',
          style: GoogleFonts.cairo(color: AppTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: provider.students.length,
            itemBuilder: (context, index) {
              final student = provider.students[index];
              final group = provider.groups.firstWhere(
                (g) => g.id == student.groupId,
                orElse: () => provider.groups.first,
              );
              return ListTile(
                title: Text(
                  student.name,
                  style: GoogleFonts.cairo(color: AppTheme.textPrimary),
                ),
                subtitle: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: AppTheme.textSecondary),
                ),
                leading: Icon(Icons.person_rounded, color: AppTheme.accentPink),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: AppTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showCompletedLessonsModal(BuildContext context, AppProvider provider) {
    final completedLessons = provider
        .getTodayLessons()
        .where((l) => l.isCompleted)
        .toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBg,
        title: Text(
          'الدروس المكتملة اليوم',
          style: GoogleFonts.cairo(color: AppTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: completedLessons.length,
            itemBuilder: (context, index) {
              final lesson = completedLessons[index];
              final group = provider.groups.firstWhere(
                (g) => g.id == lesson.groupId,
                orElse: () => provider.groups.first,
              );
              return ListTile(
                title: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: AppTheme.textPrimary),
                ),
                subtitle: Text(
                  '${lesson.dateTime.hour}:${lesson.dateTime.minute.toString().padLeft(2, '0')}',
                  style: GoogleFonts.cairo(color: AppTheme.textSecondary),
                ),
                leading: Icon(
                  Icons.check_circle_rounded,
                  color: AppTheme.success,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: AppTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showRemainingLessonsModal(BuildContext context, AppProvider provider) {
    final remainingLessons = provider
        .getTodayLessons()
        .where((l) => !l.isCompleted)
        .toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBg,
        title: Text(
          'الدروس المتبقية اليوم',
          style: GoogleFonts.cairo(color: AppTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: remainingLessons.length,
            itemBuilder: (context, index) {
              final lesson = remainingLessons[index];
              final group = provider.groups.firstWhere(
                (g) => g.id == lesson.groupId,
                orElse: () => provider.groups.first,
              );
              return ListTile(
                title: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: AppTheme.textPrimary),
                ),
                subtitle: Text(
                  '${lesson.dateTime.hour}:${lesson.dateTime.minute.toString().padLeft(2, '0')}',
                  style: GoogleFonts.cairo(color: AppTheme.textSecondary),
                ),
                leading: Icon(Icons.schedule_rounded, color: AppTheme.warning),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: AppTheme.primary),
            ),
          ),
        ],
      ),
    );
  }
}
