// Solución temporal sin usar SharedPreferences para evitar el error de canal

class PreferencesService {
  // Variable estática para almacenar el estado en memoria
  static bool _hasSeenIntroValue = false;
  
  static Future<bool> hasSeenIntro() async {
    // Simplemente devuelve el valor en memoria
    return _hasSeenIntroValue;
  }
  
  static Future<void> setHasSeenIntro(bool value) async {
    // Guarda el valor en memoria
    _hasSeenIntroValue = value;
  }
}