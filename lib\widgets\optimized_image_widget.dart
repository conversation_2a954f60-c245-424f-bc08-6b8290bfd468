import 'package:flutter/material.dart';
import '../utils/device_utils.dart';

/// A widget that displays images with optimizations for low-end devices
class OptimizedImage extends StatelessWidget {
  final String? imageUrl;
  final ImageProvider? imageProvider;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const OptimizedImage({
    super.key,
    this.imageUrl,
    this.imageProvider,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  }) : assert(imageUrl != null || imageProvider != null, 'Either imageUrl or imageProvider must be provided');

  @override
  Widget build(BuildContext context) {
    // For low-end devices, use simplified image loading
    final isLowEnd = DeviceUtils.isLowEndDevice();
    
    // Determine the appropriate filter quality based on device capability
    final filterQuality = isLowEnd ? FilterQuality.low : FilterQuality.medium;
    
    // Use the image provider if provided, otherwise create one from the URL
    final provider = imageProvider ?? NetworkImage(imageUrl!);
    
    return Image(
      image: provider,
      width: width,
      height: height,
      fit: fit,
      filterQuality: filterQuality,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        }
        return placeholder ?? Container(
          width: width,
          height: height,
          color: Colors.grey.shade800,
          child: const Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? Container(
          width: width,
          height: height,
          color: Colors.grey.shade800,
          child: const Icon(Icons.broken_image, color: Colors.white),
        );
      },
    );
  }
}