import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

enum CardType { premium, glass, accent, hover }

class EnhancedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final CardType type;
  final bool enableHoverEffect;
  final bool enablePressEffect;
  final Duration animationDuration;
  final LinearGradient? customGradient;
  final Color? customBorderColor;
  final double? customBorderRadius;

  const EnhancedCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.type = CardType.premium,
    this.enableHoverEffect = true,
    this.enablePressEffect = true,
    this.animationDuration = const Duration(milliseconds: 200),
    this.customGradient,
    this.customBorderColor,
    this.customBorderRadius,
  });

  @override
  State<EnhancedCard> createState() => _EnhancedCardState();
}

class _EnhancedCardState extends State<EnhancedCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _hoverAnimation;
  bool _isHovered = false;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    try {
      _scaleController = AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      );

      _hoverController = AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: this,
      );

      _scaleAnimation = Tween<double>(begin: 1.0, end: 0.97).animate(
        CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
      );

      _hoverAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
      );
    } catch (e) {
      // Handle initialization error
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    try {
      _scaleController.dispose();
      _hoverController.dispose();
    } catch (e) {
      // Ignore disposal errors
    }
    super.dispose();
  }

  BoxDecoration _getDecoration() {
    switch (widget.type) {
      case CardType.premium:
        return _isHovered && widget.enableHoverEffect
            ? AppTheme.premiumCardHover
            : AppTheme.premiumCard;
      case CardType.glass:
        return AppTheme.glassCard;
      case CardType.accent:
        return AppTheme.accentCard;
      case CardType.hover:
        return AppTheme.premiumCardHover;
    }
  }

  BoxDecoration _getCustomDecoration() {
    final baseDecoration = _getDecoration();

    if (widget.customGradient != null ||
        widget.customBorderColor != null ||
        widget.customBorderRadius != null) {
      return baseDecoration.copyWith(
        gradient: widget.customGradient ?? baseDecoration.gradient,
        border: widget.customBorderColor != null
            ? Border.all(color: widget.customBorderColor!, width: 1.0)
            : baseDecoration.border,
        borderRadius: widget.customBorderRadius != null
            ? BorderRadius.circular(widget.customBorderRadius!)
            : baseDecoration.borderRadius,
      );
    }

    return baseDecoration;
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.enablePressEffect || _isDisposed || !mounted) return;
    _scaleController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.enablePressEffect || _isDisposed || !mounted) return;
    _scaleController.reverse();
  }

  void _handleTapCancel() {
    if (!widget.enablePressEffect || _isDisposed || !mounted) return;
    _scaleController.reverse();
  }

  void _handleHoverEnter(PointerEvent event) {
    if (!widget.enableHoverEffect || _isDisposed || !mounted) return;
    setState(() => _isHovered = true);
    _hoverController.forward();
  }

  void _handleHoverExit(PointerEvent event) {
    if (!widget.enableHoverEffect || _isDisposed || !mounted) return;
    setState(() => _isHovered = false);
    _hoverController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: Listenable.merge([_scaleAnimation, _hoverAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: MouseRegion(
              onEnter: _handleHoverEnter,
              onExit: _handleHoverExit,
              child: Container(
                margin: widget.margin,
                decoration: _getCustomDecoration(),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: widget.onTap,
                    onTapDown: _handleTapDown,
                    onTapUp: _handleTapUp,
                    onTapCancel: _handleTapCancel,
                    borderRadius: BorderRadius.circular(
                      widget.customBorderRadius ?? 24,
                    ),
                    splashColor: Colors.white.withValues(alpha: 0.1),
                    highlightColor: Colors.white.withValues(alpha: 0.05),
                    child: AnimatedContainer(
                      duration: widget.animationDuration,
                      padding: widget.padding ?? const EdgeInsets.all(20),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Helper widget for quick card creation
class QuickCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final LinearGradient? gradient;
  final Color? borderColor;

  const QuickCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.gradient,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      onTap: onTap,
      padding: padding,
      margin: margin,
      customGradient: gradient,
      customBorderColor: borderColor,
      child: child,
    );
  }
}
