import 'package:flutter/material.dart';

class AppTheme {
  // Enhanced Modern Colors - نظام ألوان محسن ومتطور
  static const Color darkBg = Color(0xFF0A0E1A);
  static const Color cardBg = Color(0xFF1A1F2E);
  static const Color surfaceBg = Color(0xFF252B3A);
  static const Color surfaceLight = Color(0xFF2D3748);
  static const Color surfaceHover = Color(0xFF374151);

  // Primary Colors - ألوان أساسية محسنة
  static const Color primary = Color(0xFF4F46E5);
  static const Color primaryLight = Color(0xFF6366F1);
  static const Color primaryDark = Color(0xFF3730A3);
  static const Color primaryBlue = Color(0xFF06B6D4);
  static const Color primaryBlueDark = Color(0xFF0891B2);
  static const Color primaryPurple = Color(0xFF8B5CF6);
  static const Color primaryPurpleLight = Color(0xFFA78BFA);

  // Accent Colors - ألوان مميزة محسنة
  static const Color accentPink = Color(0xFFEC4899);
  static const Color accentPinkLight = Color(0xFFF472B6);
  static const Color accentGreen = Color(0xFF10B981);
  static const Color accentGreenLight = Color(0xFF34D399);
  static const Color accentOrange = Color(0xFFF97316);
  static const Color accentOrangeLight = Color(0xFFFB923C);
  static const Color accentTeal = Color(0xFF14B8A6);
  static const Color accentIndigo = Color(0xFF6366F1);

  // Status Colors - ألوان الحالة
  static const Color success = Color(0xFF22C55E);
  static const Color successLight = Color(0xFF4ADE80);
  static const Color danger = Color(0xFFEF4444);
  static const Color dangerLight = Color(0xFFF87171);
  static const Color warning = Color(0xFFFBBF24);
  static const Color warningLight = Color(0xFFFCD34D);
  static const Color info = Color(0xFF3B82F6);
  static const Color infoLight = Color(0xFF60A5FA);

  // Text Colors - ألوان النصوص المحسنة
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFE2E8F0);
  static const Color textMuted = Color(0xFF94A3B8);
  static const Color textDisabled = Color(0xFF64748B);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnAccent = Color(0xFF1E293B);

  // Enhanced Modern Gradients - تدرجات محسنة ومتطورة
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryBlue, primary, primaryPurple],
    stops: [0.0, 0.6, 1.0],
  );

  static const LinearGradient primaryGradientReverse = LinearGradient(
    begin: Alignment.bottomRight,
    end: Alignment.topLeft,
    colors: [primaryPurple, primary, primaryBlue],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [cardBg, surfaceBg, surfaceLight],
    stops: [0.0, 0.7, 1.0],
  );

  static const LinearGradient cardGradientHover = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [surfaceBg, surfaceLight, surfaceHover],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [darkBg, Color(0xFF1E293B), Color(0xFF0F172A)],
    stops: [0.0, 0.6, 1.0],
  );

  // Accent Gradients - تدرجات الألوان المميزة
  static const LinearGradient pinkGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentPink, accentPinkLight],
  );

  static const LinearGradient greenGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentGreen, accentGreenLight],
  );

  static const LinearGradient orangeGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentOrange, accentOrangeLight],
  );

  static const LinearGradient tealGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentTeal, primaryBlue],
  );

  // Status Gradients - تدرجات الحالة
  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [success, successLight],
  );

  static const LinearGradient dangerGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [danger, dangerLight],
  );

  static const LinearGradient warningGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [warning, warningLight],
  );

  static const LinearGradient infoGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [info, infoLight],
  );

  // Enhanced Card Decorations - تصاميم البطاقات المحسنة
  static final BoxDecoration premiumCard = BoxDecoration(
    gradient: cardGradient,
    borderRadius: const BorderRadius.all(Radius.circular(24)),
    border: Border.all(color: const Color(0xFF334155), width: 0.8),
    boxShadow: const [
      BoxShadow(
        color: Color(0x25000000),
        blurRadius: 24,
        offset: Offset(0, 12),
        spreadRadius: -6,
      ),
      BoxShadow(color: Color(0x15FFFFFF), blurRadius: 2, offset: Offset(0, 2)),
      BoxShadow(color: Color(0x08FFFFFF), blurRadius: 8, offset: Offset(0, -1)),
    ],
  );

  static final BoxDecoration premiumCardHover = BoxDecoration(
    gradient: cardGradientHover,
    borderRadius: const BorderRadius.all(Radius.circular(24)),
    border: Border.all(color: primaryBlue.withValues(alpha: 0.3), width: 1.2),
    boxShadow: const [
      BoxShadow(
        color: Color(0x30000000),
        blurRadius: 32,
        offset: Offset(0, 16),
        spreadRadius: -8,
      ),
      BoxShadow(color: Color(0x20FFFFFF), blurRadius: 4, offset: Offset(0, 4)),
    ],
  );

  static final BoxDecoration glassCard = BoxDecoration(
    color: Colors.white.withValues(alpha: 0.05),
    borderRadius: const BorderRadius.all(Radius.circular(20)),
    border: Border.all(color: Colors.white.withValues(alpha: 0.1), width: 1),
    boxShadow: const [
      BoxShadow(
        color: Color(0x20000000),
        blurRadius: 20,
        offset: Offset(0, 8),
        spreadRadius: -4,
      ),
    ],
  );

  static final BoxDecoration accentCard = BoxDecoration(
    gradient: primaryGradient,
    borderRadius: const BorderRadius.all(Radius.circular(20)),
    boxShadow: [
      BoxShadow(
        color: primary.withValues(alpha: 0.3),
        blurRadius: 20,
        offset: const Offset(0, 8),
        spreadRadius: -4,
      ),
    ],
  );

  // Button Decorations - تصاميم الأزرار المحسنة
  static final BoxDecoration primaryButton = BoxDecoration(
    gradient: primaryGradient,
    borderRadius: const BorderRadius.all(Radius.circular(16)),
    boxShadow: [
      BoxShadow(
        color: primary.withValues(alpha: 0.4),
        blurRadius: 16,
        offset: const Offset(0, 6),
        spreadRadius: -2,
      ),
    ],
  );

  static final BoxDecoration secondaryButton = BoxDecoration(
    gradient: cardGradient,
    borderRadius: const BorderRadius.all(Radius.circular(16)),
    border: Border.all(color: primary.withValues(alpha: 0.3), width: 1),
    boxShadow: const [
      BoxShadow(
        color: Color(0x15000000),
        blurRadius: 12,
        offset: Offset(0, 4),
        spreadRadius: -2,
      ),
    ],
  );

  static final BoxDecoration dangerButton = BoxDecoration(
    gradient: dangerGradient,
    borderRadius: const BorderRadius.all(Radius.circular(16)),
    boxShadow: [
      BoxShadow(
        color: danger.withValues(alpha: 0.4),
        blurRadius: 16,
        offset: const Offset(0, 6),
        spreadRadius: -2,
      ),
    ],
  );

  static final BoxDecoration successButton = BoxDecoration(
    gradient: successGradient,
    borderRadius: const BorderRadius.all(Radius.circular(16)),
    boxShadow: [
      BoxShadow(
        color: success.withValues(alpha: 0.4),
        blurRadius: 16,
        offset: const Offset(0, 6),
        spreadRadius: -2,
      ),
    ],
  );

  // Light Theme Colors
  static const Color lightBg = Color(0xFFF8FAFC);
  static const Color lightCardBg = Color(0xFFFFFFFF);
  static const Color lightSurfaceBg = Color(0xFFF1F5F9);
  static const Color lightSurfaceLight = Color(0xFFE2E8F0);
  static const Color lightSurfaceHover = Color(0xFFCBD5E1);
  static const Color lightTextPrimary = Color(0xFF1E293B);
  static const Color lightTextSecondary = Color(0xFF475569);
  static const Color lightTextMuted = Color(0xFF64748B);
  static const Color lightTextDisabled = Color(0xFF94A3B8);

  // Light Theme Gradients
  static const LinearGradient lightBackgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [lightBg, Color(0xFFE2E8F0), Color(0xFFF1F5F9)],
    stops: [0.0, 0.6, 1.0],
  );

  static const LinearGradient lightCardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [lightCardBg, Color(0xFFFAFAFA)],
  );

  // Dark Theme
  static ThemeData get darkTheme => ThemeData(
    primaryColor: primary,
    scaffoldBackgroundColor: darkBg,
    colorScheme: const ColorScheme.dark(
      primary: primary,
      secondary: info,
      surface: cardBg,
      surfaceTint: cardBg,
      error: danger,
    ),
    appBarTheme: const AppBarTheme(backgroundColor: cardBg, elevation: 0),
    cardTheme: CardThemeData(
      color: cardBg,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primary,
        foregroundColor: Colors.white,
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(foregroundColor: primary),
    ),
    iconTheme: const IconThemeData(color: primary),
  );

  // Light Theme
  static ThemeData get lightTheme => ThemeData(
    primaryColor: primary,
    scaffoldBackgroundColor: lightBg,
    colorScheme: const ColorScheme.light(
      primary: primary,
      secondary: info,
      surface: lightCardBg,
      surfaceTint: lightCardBg,
      error: danger,
    ),
    appBarTheme: const AppBarTheme(backgroundColor: lightCardBg, elevation: 0),
    cardTheme: CardThemeData(
      color: lightCardBg,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primary,
        foregroundColor: Colors.white,
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(foregroundColor: primary),
    ),
    iconTheme: const IconThemeData(color: primary),
  );

  // Legacy getter for backward compatibility
  static ThemeData get theme => darkTheme;

  // Helper methods for theme-aware colors
  static Color getBackgroundColor(bool isDark) => isDark ? darkBg : lightBg;
  static Color getCardColor(bool isDark) => isDark ? cardBg : lightCardBg;
  static Color getSurfaceColor(bool isDark) =>
      isDark ? surfaceBg : lightSurfaceBg;
  static Color getTextPrimaryColor(bool isDark) =>
      isDark ? textPrimary : lightTextPrimary;
  static Color getTextSecondaryColor(bool isDark) =>
      isDark ? textSecondary : lightTextSecondary;
  static Color getTextMutedColor(bool isDark) =>
      isDark ? textMuted : lightTextMuted;

  static LinearGradient getBackgroundGradient(bool isDark) =>
      isDark ? backgroundGradient : lightBackgroundGradient;
  static LinearGradient getCardGradient(bool isDark) =>
      isDark ? cardGradient : lightCardGradient;
}
