import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';

class SpeechService {
  static final SpeechToText _speechToText = SpeechToText();
  static final FlutterTts _flutterTts = FlutterTts();
  static bool _isInitialized = false;

  static Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      // Check microphone permission (handled by speech_to_text package)
      
      // Initialize speech to text
      final speechAvailable = await _speechToText.initialize(
        onError: (error) => debugPrint('Speech error: $error'),
        onStatus: (status) => debugPrint('Speech status: $status'),
      );
      
      // Initialize text to speech
      await _flutterTts.setLanguage('ar-SA');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);
      
      _isInitialized = speechAvailable;
      return _isInitialized;
    } catch (e) {
      debugPrint('Speech service initialization error: $e');
      return false;
    }
  }

  static Future<String?> startListening() async {
    try {
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) return null;
      }

      if (!_speechToText.isAvailable) return null;

      String? recognizedText;
      bool isComplete = false;
      
      await _speechToText.listen(
        onResult: (result) {
          recognizedText = result.recognizedWords;
          if (result.finalResult) {
            isComplete = true;
          }
        },
        listenFor: const Duration(seconds: 10),
        pauseFor: const Duration(seconds: 3),
        listenOptions: SpeechListenOptions(
          partialResults: true,
          onDevice: false,
          listenMode: ListenMode.confirmation,
        ),
        localeId: 'ar',
      );

      // Wait for listening to complete or timeout
      int attempts = 0;
      while (_speechToText.isListening && attempts < 100) {
        await Future.delayed(const Duration(milliseconds: 100));
        attempts++;
        if (isComplete) break;
      }

      return recognizedText;
    } catch (e) {
      debugPrint('Speech listening error: $e');
      return null;
    }
  }

  static Future<void> speak(String text) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      await _flutterTts.speak(text);
    } catch (e) {
      debugPrint('TTS error: $e');
    }
  }

  static void stopListening() {
    _speechToText.stop();
  }

  static void stopSpeaking() {
    _flutterTts.stop();
  }

  static bool get isListening => _speechToText.isListening;
  static bool get isInitialized => _isInitialized;
}